name: Deploy to Test Server

on:
    push:
        branches:
            - dev

jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Install sshpass
              run: |
                  sudo apt-get update
                  sudo apt-get install -y sshpass

            - name: Deploy to Linux Remote Server
              run: |
                  sshpass -p "${{ secrets.TEST_SSH_PASSWORD }}" ssh -vvv -o StrictHostKeyChecking=no "${{ secrets.TEST_HOST }}" << 'EOF'
                  set -e

                  git config --global url."https://${{ secrets.PAT }}@github.com/".insteadOf https://github.com/

                  cd /home/<USER>

                  if [ ! -d "emissium-euclid-api/.git" ]; then
                    git clone --branch dev https://github.com/emissium/emissium-euclid-api.git emissium-euclid-api
                  fi

                  cd emissium-euclid-api
                  git checkout dev
                  git pull origin dev

                  cat > docker-compose.yml << EOL
                  version: '3'
                  services:
                      euclid-api:
                          build: .
                          ports:
                              - '4000:4000'
                          environment:
                              - NODE_ENV=production
                              - PORT=4000
                              - SUPABASE_URL=https://exjwvlvcalqhqfngyzbw.supabase.co
                              - SUPABASE_SECRET_KEY=${{ secrets.SUPABASE_SECRET_KEY_DEV }}
                              - INFLUX_URL=https://introducer.emissium.net/influxdb
                              - INFLUX_TOKEN=${{ secrets.INFLUX_TOKEN }}
                              - INFLUX_ORG=emissium
                              - SUPABASE_THALES_URL=https://exjwvlvcalqhqfngyzbw.supabase.co
                              - SUPABASE_THALES_SECRET_KEY=${{ secrets.SUPABASE_SECRET_KEY_DEV }}
                              - GOD_TOKEN=${{ secrets.GOD_TOKEN }}
                          volumes:
                              - ./log:/usr/src/app/log # Persist logs outside the container
                          command: npm start
                  EOL

                  export NVM_DIR="$HOME/.nvm"
                  [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"

                  docker compose build
                  docker compose down -v
                  docker compose up -d --remove-orphans

                  EOF
