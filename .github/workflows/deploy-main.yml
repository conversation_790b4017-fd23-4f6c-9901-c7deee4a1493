name: Deploy to Server

on:
    push:
        branches:
            - main

jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout repository
              uses: actions/checkout@v2

            - name: Set up SSH
              uses: webfactory/ssh-agent@v0.5.3
              with:
                  ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

            - name: Deploy to Linux Remote Server
              run: |
                  ssh -o StrictHostKeyChecking=no ${{ secrets.USERNAME }}@${{ secrets.HOST }} << 'EOF'
                  set -e
                  git config --global user.email "<EMAIL>"
                  git config --global user.name "alsantek"
                  git config --global url.https://${{ secrets.PAT }}@github.com/.insteadOf https://github.com/
                  cd /home/<USER>/emissium-euclid-api
                  git reset --hard
                  git pull origin main
                  cat > docker-compose.yml << EOL
                  version: '3'
                  services:
                      euclid-api:
                          build: .
                          ports:
                              - '4000:4000'
                          environment:
                              - NODE_ENV=production
                              - PORT=4000
                              - SUPABASE_URL=https://afrdxeeulqieluntvmmy.supabase.co
                              - SUPABASE_SECRET_KEY=${{ secrets.SUPABASE_SECRET_KEY }}
                              - INFLUX_URL=https://introducer.emissium.net/influxdb
                              - INFLUX_TOKEN=${{ secrets.INFLUX_TOKEN }}
                              - INFLUX_ORG=emissium
                              - SUPABASE_THALES_URL=https://afrdxeeulqieluntvmmy.supabase.co
                              - SUPABASE_THALES_SECRET_KEY=${{ secrets.SUPABASE_SECRET_KEY }}
                              - GOD_TOKEN=${{ secrets.GOD_TOKEN }}
                          volumes:
                              - ./log:/usr/src/app/log # Persist logs outside the container
                          command: npm start
                  EOL

                  export NVM_DIR="$HOME/.nvm"
                  [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"
                  docker compose build
                  docker compose down -v
                  docker compose up -d --remove-orphans
                  EOF
