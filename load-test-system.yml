config:
  target: "https://data.emissium.io"
  processor: "./load-test-helpers.js"
  defaults:
    headers:
      Authorization: "Bearer em_qwertyuiopasdfghjklzxcvbnm123"
      Content-Type: "application/json"
  phases:
    - duration: 30
      arrivalRate: 5
      name: "System endpoints - normal load"
    - duration: 30
      arrivalRate: 20
      name: "System endpoints - high load"

scenarios:
  - name: "System Heartbeat Tests"
    beforeScenario: "setDefaultHeadersIfNeeded"
    flow:
      # Test system heartbeat with valid auth
      - get:
          name: "GET_system_heartbeat_valid"
          url: "/api/v1/system/heartbeat"
          expect:
            - statusCode: 200
            - contentType: "application/json"
            - hasProperty: "status"
            - hasProperty: "message"
            - equals:
                - "{{ status }}"
                - "success"
          afterResponse: "logResponse"

      # Test system heartbeat with invalid auth
      - get:
          name: "GET_system_heartbeat_invalid_auth"
          url: "/api/v1/system/heartbeat"
          headers:
            Authorization: "Bearer invalid_token"
          expect:
            - statusCode: 401
          afterResponse: "logResponse" 
