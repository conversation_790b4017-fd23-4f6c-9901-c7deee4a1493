config:
  target: "https://data.emissium.io"
  processor: "./load-test-helpers.js"
  defaults:
    headers:
      Authorization: "Bearer em_qwertyuiopasdfghjklzxcvbnm123"
      Content-Type: "application/json"
  plugins:
    metrics-by-endpoint: {}
  ensure:
    maxErrorRate: 50  # Very high threshold for extreme tests
  useOnlyRequestNames: true
  phases:
    # Phase 1: Normal traffic
    - duration: 30
      arrivalRate: 10
      name: "Normal traffic"

    # Phase 2: Sudden massive spike (100x)
    - duration: 20
      arrivalRate: 1000
      name: "DDoS Spike 1 - 1000 req/sec"

    # Phase 3: Brief recovery
    - duration: 30
      arrivalRate: 10
      name: "Short recovery"

    # Phase 4: Extreme spike (200x)
    - duration: 20
      arrivalRate: 2000
      name: "DDoS Spike 2 - 2000 req/sec"

    # Phase 5: Recovery attempt
    - duration: 60
      arrivalRate: 10
      name: "Recovery period"

scenarios:
  - name: "DDoS Simulation"
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      # Rapid-fire single requests
      - get:
          name: "DDOS_single_zone"
          url: "/api/v1/zones/{{ randomNutsId }}"
          expect:
            - statusCode: [200, 429, 503]  # Accept rate limit and service unavailable responses
          afterResponse: "logResponse"

      # Heavy batch requests
      - get:
          name: "DDOS_batch_zones"
          url: "/api/v1/zones/TR61,TR52,TR62,TR63,TR71,TR72,TRC1,TRC2,TRC3,TR10,TR82,TR83,TR81"
          expect:
            - statusCode: [200, 429, 503]
          afterResponse: "logResponse"

      # System heartbeat
      - get:
          name: "DDOS_heartbeat"
          url: "/api/v1/system/heartbeat"
          expect:
            - statusCode: [200, 429, 503]
          afterResponse: "logResponse"

      # Heavy impact request
      - get:
          name: "DDOS_impact_data"
          url: "/api/v1/impacts/{{ randomIndicator }}/{{ randomMode }}/{{ batchNutsIds }}"
          expect:
            - statusCode: [200, 429, 503]
          afterResponse: "logResponse"

      # Minimal think time (simulating automated attacks)
      - think: 0.05

  - name: "Aggressive Error Triggering"
    weight: 2  # More frequent invalid requests
    beforeScenario: "setDefaultHeadersIfNeeded"
    flow:
      # Invalid requests
      - get:
          name: "DDOS_invalid_zone"
          url: "/api/v1/zones/INVALID_{{ $randomString(10) }}"
          expect:
            - statusCode: [404, 429, 503]
          afterResponse: "logResponse"

      # Invalid auth
      - get:
          name: "DDOS_invalid_auth"
          url: "/api/v1/zones/TR61"
          headers:
            Authorization: "Bearer invalid_token_{{ $randomString(32) }}"
          expect:
            - statusCode: [401, 429, 503]
          afterResponse: "logResponse"

      # No think time
      - think: 0.01 
