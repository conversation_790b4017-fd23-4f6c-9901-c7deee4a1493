config:
  target: "https://data.emissium.io"
  processor: "./load-test-helpers.js"
  defaults:
    headers:
      Authorization: "Bearer em_qwertyuiopasdfghjklzxcvbnm123"
      Content-Type: "application/json"
  phases:
    - duration: 60
      arrivalRate: 5
      name: "Zones endpoints - normal load"
    - duration: 60
      arrivalRate: 20
      name: "Zones endpoints - high load"

scenarios:
  - name: "Zones API Tests"
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      # Get all zones
      - get:
          name: "GET_all_zones"
          url: "/api/v1/zones"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
      # Get zones by level
      - get:
          name: "GET_zones_by_level_0"
          url: "/api/v1/zones?level=0"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
      - get:
          name: "GET_zones_by_level_1"
          url: "/api/v1/zones?level=1"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
      # Get zones by status
      - get:
          name: "GET_zones_active"
          url: "/api/v1/zones?status=active"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
      # Get specific zone
      - get:
          name: "GET_specific_zone"
          url: "/api/v1/zones/{{ randomNutsId }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
            - hasProperty: "nuts_id"
            - hasProperty: "name"
            - hasProperty: "is_active"
          afterResponse: "logResponse"
      
      # Get multiple zones
      - get:
          name: "GET_multiple_zones"
          url: "/api/v1/zones/{{ batchNutsIds }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
  - name: "Zones API Error Tests"
    beforeScenario: "setDefaultHeadersIfNeeded"
    flow:
      # Invalid NUTS ID
      - get:
          name: "GET_invalid_zone"
          url: "/api/v1/zones/INVALID_ID"
          expect:
            - statusCode: 404
          afterResponse: "logResponse"
      
      # Invalid level parameter
      - get:
          name: "GET_invalid_level"
          url: "/api/v1/zones?level=99"
          expect:
            - statusCode: 400
          afterResponse: "logResponse"
      
      # Invalid status parameter
      - get:
          name: "GET_invalid_status"
          url: "/api/v1/zones?status=invalid"
          expect:
            - statusCode: 400
          afterResponse: "logResponse" 
