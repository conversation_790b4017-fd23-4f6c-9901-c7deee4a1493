config:
  target: "https://data.emissium.io"
  plugins:
    metrics-by-endpoint: {}
    expect: {}
  ensure:
    maxErrorRate: 5
  useOnlyRequestNames: true
  defaults:
    headers:
      Authorization: "Bearer em_qwertyuiopasdfghjklzxcvbnm123"
      Content-Type: "application/json"
  processor: "./load-test-helpers.js"
  phases:
    # Standard load test phases
    - name: "Warm up"
      duration: 60
      rampTo: 10
      maxVusers: 10
    - name: "Ramp up load"
      duration: 120
      rampTo: 20
      maxVusers: 20
    - name: "Sustained load"
      duration: 240
      rampTo: 30
      maxVusers: 30
