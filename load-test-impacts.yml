config:
  target: "https://data.emissium.io"
  processor: "./load-test-helpers.js"
  defaults:
    headers:
      Authorization: "Bearer em_qwertyuiopasdfghjklzxcvbnm123"
      Content-Type: "application/json"
  phases:
    - duration: 60
      arrivalRate: 5
      name: "Impacts endpoints - normal load"
    - duration: 60
      arrivalRate: 15
      name: "Impacts endpoints - medium load"
    - duration: 30
      arrivalRate: 30
      name: "Impacts endpoints - high load"

scenarios:
  - name: "Impacts API Tests"
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      # Get latest impact data for specific indicator and NUTS ID
      - get:
          name: "GET_impact_last_single"
          url: "/api/v1/impacts/{{ randomIndicator }}/last/{{ randomNutsId }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
            - hasProperty: "indicator"
            - hasProperty: "units"
            - hasProperty: "methodology"
            - hasProperty: "data"
          afterResponse: "logResponse"
      
      # Get historical impact data for specific indicator and NUTS ID
      - get:
          name: "GET_impact_historical_single"
          url: "/api/v1/impacts/{{ randomIndicator }}/historical/{{ randomNutsId }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
            - hasProperty: "indicator"
            - hasProperty: "units"
            - hasProperty: "methodology"
            - hasProperty: "data"
          afterResponse: "logResponse"
      
      # Get latest impact data for specific indicator and multiple NUTS IDs
      - get:
          name: "GET_impact_last_multiple"
          url: "/api/v1/impacts/{{ randomIndicator }}/last/{{ batchNutsIds }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
      # Get historical impact data for specific indicator and multiple NUTS IDs
      - get:
          name: "GET_impact_historical_multiple"
          url: "/api/v1/impacts/{{ randomIndicator }}/historical/{{ batchNutsIds }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
      # Get specific impact with query parameters
      - get:
          name: "GET_impact_with_params"
          url: "/api/v1/impacts/{{ randomIndicator }}/{{ randomMode }}/{{ randomNutsId }}?from=2023-01-01&to=2023-12-31"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
  
  - name: "Impacts API Error Tests"
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      # Invalid indicator
      - get:
          name: "GET_impact_invalid_indicator"
          url: "/api/v1/impacts/invalid_indicator/last/{{ randomNutsId }}"
          expect:
            - statusCode: 404
          afterResponse: "logResponse"
      
      # Invalid mode
      - get:
          name: "GET_impact_invalid_mode"
          url: "/api/v1/impacts/{{ randomIndicator }}/invalid_mode/{{ randomNutsId }}"
          expect:
            - statusCode: 400
          afterResponse: "logResponse"
      
      # Invalid NUTS ID
      - get:
          name: "GET_impact_invalid_nuts"
          url: "/api/v1/impacts/{{ randomIndicator }}/last/INVALID_ID"
          expect:
            - statusCode: 404
          afterResponse: "logResponse"
      
      # Invalid date parameters
      - get:
          name: "GET_impact_invalid_dates"
          url: "/api/v1/impacts/{{ randomIndicator }}/historical/{{ randomNutsId }}?from=invalid&to=invalid"
          expect:
            - statusCode: 400
          afterResponse: "logResponse" 
