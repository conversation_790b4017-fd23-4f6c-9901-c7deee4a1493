config:
  target: "https://data.emissium.io"
  processor: "./load-test-helpers.js"
  defaults:
    headers:
      Authorization: "Bearer em_qwertyuiopasdfghjklzxcvbnm123"
      Content-Type: "application/json"
  phases:
    - duration: 30
      arrivalRate: 5
      name: "NUTS endpoints - normal load"
    - duration: 30
      arrivalRate: 20
      name: "NUTS endpoints - high load"

scenarios:
  - name: "NUTS API Tests"
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      # Get NUTS data
      - get:
          name: "GET_nuts_data"
          url: "/api/v1/nuts/{{ randomNutsId }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
      # Get NUTS children
      - get:
          name: "GET_nuts_children"
          url: "/api/v1/nuts/{{ randomNutsId }}/children"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
      # Get NUTS parent
      - get:
          name: "GET_nuts_parent"
          url: "/api/v1/nuts/{{ randomNutsId }}/parent"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
  
  - name: "NUTS API Error Tests"
    beforeScenario: "setDefaultHeadersIfNeeded"
    flow:
      # Invalid NUTS ID
      - get:
          name: "GET_nuts_invalid_id"
          url: "/api/v1/nuts/INVALID_ID"
          expect:
            - statusCode: 404
          afterResponse: "logResponse"
      
      # Invalid auth
      - get:
          name: "GET_nuts_invalid_auth"
          url: "/api/v1/nuts/TR61"
          headers:
            Authorization: "Bearer invalid_token"
          expect:
            - statusCode: 401
          afterResponse: "logResponse" 
