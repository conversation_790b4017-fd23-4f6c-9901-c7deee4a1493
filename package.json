{"name": "emissium-euclid-api", "version": "1.1.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "start": "node dist/app.js", "dev": "nodemon", "format": "prettier --write \"src/**/*.ts\"", "build": "tsc", "syncThales": "node scripts/syncThalesSchema.js", "load-test:system": "artillery run load-test-system.yml", "load-test:zones": "artillery run load-test-zones.yml", "load-test:impacts": "artillery run load-test-impacts.yml", "load-test:indicators": "artillery run load-test-indicators.yml", "load-test:nuts": "artillery run load-test-nuts.yml", "load-test:spike": "artillery run load-test-spike.yml", "load-test:extreme": "artillery run load-test-extreme-spike.yml", "load-test:all": "artillery run --record --key a9_SJpkOw4PjIv4hqXMKPr8DE5YeRBlarSW load-test-all.yml"}, "keywords": [], "author": "Emissium", "license": "ISC", "dependencies": {"@influxdata/influxdb-client": "^1.34.0", "@supabase/supabase-js": "^2.45.1", "@types/node-cache": "^4.2.5", "body-parser": "^1.20.2", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.4.0", "morgan": "^1.10.0", "node-cache": "^5.1.2", "rotating-file-stream": "^3.2.3", "swagger-ui-express": "^5.0.1", "yaml": "^2.5.0"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/morgan": "^1.9.9", "@types/node": "^20.14.14", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.6", "artillery": "^2.0.22", "jest": "^29.7.0", "nodemon": "^3.1.7", "prettier": "^3.3.3", "supertest": "^7.0.0", "ts-jest": "^29.2.4", "ts-node": "^10.9.2", "typescript": "^5.5.4"}}