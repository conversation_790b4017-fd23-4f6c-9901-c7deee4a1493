config:
  target: "https://data.emissium.io"
  processor: "./load-test-helpers.js"
  defaults:
    headers:
      Authorization: "Bearer em_qwertyuiopasdfghjklzxcvbnm123"
      Content-Type: "application/json"
  plugins:
    metrics-by-endpoint: {}
    expect: {}
  ensure:
    maxErrorRate: 25
  useOnlyRequestNames: true
  phases:
    - name: "Warm up"
      duration: 60
      rampTo: 10
      maxVusers: 10
    - name: "Ramp up load"
      duration: 120
      rampTo: 20
      maxVusers: 20
    - name: "Sustained load"
      duration: 240
      rampTo: 40
      maxVusers: 40

scenarios:
  # System endpoints
  - name: "System API Tests"
    weight: 1
    beforeScenario: "setDefaultHeadersIfNeeded"
    flow:
      - get:
          name: "GET_system_heartbeat"
          url: "/api/v1/system/heartbeat"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"

  # Zones endpoints
  - name: "Zones API Tests"
    weight: 3
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      - get:
          name: "GET_all_zones"
          url: "/api/v1/zones"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"
      
      - get:
          name: "GET_zones_by_level"
          url: "/api/v1/zones?level=0"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"
      
      - get:
          name: "GET_specific_zone"
          url: "/api/v1/zones/{{ randomNutsId }}"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"
      
      - get:
          name: "GET_multiple_zones"
          url: "/api/v1/zones/{{ batchNutsIds }}"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"

  # Impacts endpoints
  - name: "Impacts API Tests"
    weight: 3
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      - get:
          name: "GET_impact_last_single"
          url: "/api/v1/impacts/{{ randomIndicator }}/last/{{ randomNutsId }}"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"
      
      - get:
          name: "GET_impact_historical_single"
          url: "/api/v1/impacts/{{ randomIndicator }}/historical/{{ randomNutsId }}"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"
      
      - get:
          name: "GET_impact_last_multiple"
          url: "/api/v1/impacts/{{ randomIndicator }}/last/{{ batchNutsIds }}"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"

  # Indicators endpoints
  - name: "Indicators API Tests"
    weight: 1
    beforeScenario: "setDefaultHeadersIfNeeded"
    flow:
      - get:
          name: "GET_all_indicators"
          url: "/api/v1/indicators"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"

  # NUTS endpoints
  - name: "NUTS API Tests"
    weight: 2
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      - get:
          name: "GET_nuts_data"
          url: "/api/v1/nuts/{{ randomNutsId }}"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"
      
      - get:
          name: "GET_nuts_children"
          url: "/api/v1/nuts/{{ randomNutsId }}/children"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"
      
      - get:
          name: "GET_nuts_parent"
          url: "/api/v1/nuts/{{ randomNutsId }}/parent"
          expect:
            - statusCode: 200
          afterResponse: "logResponse"

  # Error cases
  - name: "Error Tests"
    weight: 1
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      - get:
          name: "GET_invalid_zone"
          url: "/api/v1/zones/INVALID_ID"
          expect:
            - statusCode: 404
          afterResponse: "logResponse"
      
      - get:
          name: "GET_invalid_indicator"
          url: "/api/v1/impacts/invalid_indicator/last/{{ randomNutsId }}"
          expect:
            - statusCode: 404
          afterResponse: "logResponse" 
