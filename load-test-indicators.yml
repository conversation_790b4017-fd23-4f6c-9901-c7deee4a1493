config:
  target: "https://data.emissium.io"
  processor: "./load-test-helpers.js"
  defaults:
    headers:
      Authorization: "Bearer em_qwertyuiopasdfghjklzxcvbnm123"
      Content-Type: "application/json"
  phases:
    - duration: 30
      arrivalRate: 5
      name: "Indicators endpoints - normal load"
    - duration: 30
      arrivalRate: 20
      name: "Indicators endpoints - high load"

scenarios:
  - name: "Indicators API Tests"
    beforeScenario: "setDefaultHeadersIfNeeded"
    flow:
      # Get all indicators
      - get:
          name: "GET_all_indicators"
          url: "/api/v1/indicators"
          expect:
            - statusCode: 200
            - contentType: "application/json"
          afterResponse: "logResponse"
      
      # Get indicators with invalid auth
      - get:
          name: "GET_indicators_invalid_auth"
          url: "/api/v1/indicators"
          headers:
            Authorization: "Bearer invalid_token"
          expect:
            - statusCode: 401
          afterResponse: "logResponse" 
