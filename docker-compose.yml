services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "2999:2999"
    environment:
      - NODE_ENV=development
      - PORT=2999
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SECRET_KEY=${SUPABASE_SECRET_KEY}
      - SUPABASE_THALES_URL=${SUPABASE_THALES_URL}
      - SUPABASE_THALES_SECRET_KEY=${SUPABASE_THALES_SECRET_KEY}
      - INFLUX_URL=${INFLUX_URL}
      - INFLUX_TOKEN=${INFLUX_TOKEN}
      - INFLUX_ORG=${INFLUX_ORG}
      - BEARER_TOKEN=${BEARER_TOKEN}
      - GOD_TOKEN=${GOD_TOKEN}
      - API_URL=${API_URL}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    env_file:
      - .env
    depends_on:
      - redis
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - emissium-network

  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    env_file:
      - .env
    networks:
      - emissium-network

volumes:
  redis_data:

networks:
  emissium-network:
    driver: bridge 