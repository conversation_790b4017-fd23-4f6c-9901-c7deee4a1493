#!/usr/bin/env node

/**
 * Redis Integration Test Script
 * Tests the Redis functionality without requiring full environment setup
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Redis Integration Test Suite');
console.log('================================\n');

// Test 1: Check if Redis files exist and have correct structure
console.log('📁 Test 1: File Structure Check');
const redisFiles = [
  'src/lib/redis.ts',
  'src/lib/redis-utils.ts',
  'src/lib/cache-manager.ts',
  'src/middleware/advancedCacheMiddleware.ts',
  'src/routes/redis-example.ts',
  'src/routes/cache-management.ts'
];

let fileTestsPassed = 0;
redisFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file} exists`);
    fileTestsPassed++;
  } else {
    console.log(`   ❌ ${file} missing`);
  }
});

console.log(`   📊 File tests: ${fileTestsPassed}/${redisFiles.length} passed\n`);

// Test 2: Check TypeScript syntax and imports
console.log('🔍 Test 2: TypeScript Syntax Check');

function checkTypeScriptFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check for basic syntax issues
    const issues = [];
    
    // Check for proper imports
    if (filePath.includes('redis.ts')) {
      if (!content.includes("import { createClient } from 'redis'")) {
        issues.push('Missing Redis import');
      }
      if (!content.includes('export const connectRedis')) {
        issues.push('Missing connectRedis export');
      }
    }
    
    if (filePath.includes('redis-utils.ts')) {
      if (!content.includes("import { getRedisClient } from './redis'")) {
        issues.push('Missing Redis client import');
      }
      if (!content.includes('export const setCache')) {
        issues.push('Missing setCache export');
      }
    }
    
    if (filePath.includes('cache-manager.ts')) {
      if (!content.includes('export class CacheManager')) {
        issues.push('Missing CacheManager class export');
      }
    }
    
    // Check for syntax errors (basic)
    const openBraces = (content.match(/{/g) || []).length;
    const closeBraces = (content.match(/}/g) || []).length;
    if (openBraces !== closeBraces) {
      issues.push('Mismatched braces');
    }
    
    const openParens = (content.match(/\(/g) || []).length;
    const closeParens = (content.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      issues.push('Mismatched parentheses');
    }
    
    return issues;
  } catch (error) {
    return [`Error reading file: ${error.message}`];
  }
}

let syntaxTestsPassed = 0;
redisFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const issues = checkTypeScriptFile(file);
    if (issues.length === 0) {
      console.log(`   ✅ ${file} syntax OK`);
      syntaxTestsPassed++;
    } else {
      console.log(`   ❌ ${file} issues: ${issues.join(', ')}`);
    }
  }
});

console.log(`   📊 Syntax tests: ${syntaxTestsPassed}/${redisFiles.length} passed\n`);

// Test 3: Check app.ts integration
console.log('🔗 Test 3: App Integration Check');
const appPath = 'src/app.ts';
if (fs.existsSync(appPath)) {
  const appContent = fs.readFileSync(appPath, 'utf8');
  const integrationChecks = [
    {
      check: "import { connectRedis } from './lib/redis'",
      name: 'Redis import'
    },
    {
      check: "import redisExample from './routes/redis-example'",
      name: 'Redis example route import'
    },
    {
      check: "import cacheManagement from './routes/cache-management'",
      name: 'Cache management route import'
    },
    {
      check: "import { advancedCacheMiddleware, updateCacheOnChange } from './middleware/advancedCacheMiddleware'",
      name: 'Advanced cache middleware import'
    },
    {
      check: "connectRedis().catch(console.error)",
      name: 'Redis connection initialization'
    },
    {
      check: "app.use('/api/v1/redis', redisExample)",
      name: 'Redis routes registration'
    },
    {
      check: "app.use('/api/v1/cache', authenticateToken(), cacheManagement)",
      name: 'Cache management routes registration'
    }
  ];
  
  let integrationTestsPassed = 0;
  integrationChecks.forEach(({ check, name }) => {
    if (appContent.includes(check)) {
      console.log(`   ✅ ${name}`);
      integrationTestsPassed++;
    } else {
      console.log(`   ❌ ${name} missing`);
    }
  });
  
  console.log(`   📊 Integration tests: ${integrationTestsPassed}/${integrationChecks.length} passed\n`);
} else {
  console.log('   ❌ src/app.ts not found\n');
}

// Test 4: Check package.json dependencies
console.log('📦 Test 4: Dependencies Check');
const packagePath = 'package.json';
if (fs.existsSync(packagePath)) {
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const requiredDeps = ['redis'];
  const requiredDevDeps = ['@types/redis'];
  
  let depTestsPassed = 0;
  requiredDeps.forEach(dep => {
    if (packageContent.dependencies && packageContent.dependencies[dep]) {
      console.log(`   ✅ ${dep}: ${packageContent.dependencies[dep]}`);
      depTestsPassed++;
    } else {
      console.log(`   ❌ ${dep} missing from dependencies`);
    }
  });
  
  requiredDevDeps.forEach(dep => {
    if (packageContent.devDependencies && packageContent.devDependencies[dep]) {
      console.log(`   ✅ ${dep}: ${packageContent.devDependencies[dep]}`);
      depTestsPassed++;
    } else {
      console.log(`   ❌ ${dep} missing from devDependencies`);
    }
  });
  
  console.log(`   📊 Dependency tests: ${depTestsPassed}/${requiredDeps.length + requiredDevDeps.length} passed\n`);
} else {
  console.log('   ❌ package.json not found\n');
}

// Test 5: Check for merge conflict markers
console.log('🔀 Test 5: Merge Conflict Check');
let conflictTestsPassed = 0;
const allFiles = [
  'src/app.ts',
  'src/routes/calculate.ts',
  ...redisFiles
];

allFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    const conflictMarkers = ['<<<<<<<', '=======', '>>>>>>>'];
    const hasConflicts = conflictMarkers.some(marker => content.includes(marker));
    
    if (!hasConflicts) {
      console.log(`   ✅ ${file} - no conflicts`);
      conflictTestsPassed++;
    } else {
      console.log(`   ❌ ${file} - contains merge conflict markers`);
    }
  }
});

console.log(`   📊 Conflict tests: ${conflictTestsPassed}/${allFiles.length} passed\n`);

// Summary
console.log('📋 Test Summary');
console.log('===============');
const totalTests = fileTestsPassed + syntaxTestsPassed + conflictTestsPassed;
const maxTests = (redisFiles.length * 3) + 7; // Approximate

console.log(`✅ Tests passed: ${totalTests}`);
console.log(`📊 Overall status: ${totalTests >= maxTests * 0.8 ? '🟢 GOOD' : '🟡 NEEDS ATTENTION'}`);

if (totalTests >= maxTests * 0.8) {
  console.log('\n🎉 Redis integration appears to be working correctly!');
  console.log('   - All Redis files are present and properly structured');
  console.log('   - TypeScript syntax looks good');
  console.log('   - App.ts integration is complete');
  console.log('   - Dependencies are properly configured');
  console.log('   - No merge conflicts detected');
} else {
  console.log('\n⚠️  Some issues detected. Please review the failed tests above.');
}

console.log('\n🔧 Next steps:');
console.log('   1. Set up Redis server (REDIS_URL environment variable)');
console.log('   2. Run full integration tests with proper environment');
console.log('   3. Test API endpoints manually or with Postman');
console.log('   4. Monitor Redis connection in production logs');
