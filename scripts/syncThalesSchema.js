const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Singleton pattern
let thalesClient = null;
let client = null;

const sbUrl = process.env.SUPABASE_URL;
const sbSecretKey = process.env.SUPABASE_SECRET_KEY;

if (!sbUrl || !sbSecretKey) {
    throw new Error('Missing SUPABASE_URL or SUPABASE_SECRET_KEY environment variable');
}

client = createClient(sbUrl, sbSecretKey);

const sbUrlInternal = process.env.SUPABASE_THALES_URL;
const sbSecretKeyInternal = process.env.SUPABASE_THALES_SECRET_KEY;

if (!sbUrlInternal || !sbSecretKeyInternal) {
    throw new Error(
        'Missing SUPABASE_THALES_URL or SUPABASE_THALES_SECRET_KEY environment variable'
    );
}

thalesClient = createClient(sbUrlInternal, sbSecretKeyInternal);

(async () => {
    const { data, error: err1 } = await thalesClient
        .schema('thales')
        .from('tables_to_sync_emissium_api')
        .select('*');
    TABLES_TO_SYNC = data.map((x) => x.table_name);
    console.log(`Starting Syncing for ${TABLES_TO_SYNC}`);

    TABLES_TO_SYNC.forEach(async (tableName) => {
        try {
            const { data, error: err1 } = await thalesClient
                .schema('thales')
                .from(tableName)
                .select('*');

            if (err1) {
                console.error('Error fetching data:', err1);
            } else {
                const { data: _, error: err2 } = await client
                    .schema('thales')
                    .from(tableName)
                    .upsert(data);
                if (err2) console.error('Error fetching data:', err2);
            }
            console.log(`Synced Table: ${tableName}`);
        } catch (err) {
            console.error('Unexpected error:', err);
        }
    });
})();
