# Emissium Euclid API - Project Intelligence

## Project Overview
This is a production Node.js/Express API serving environmental and energy data. The system replaces a legacy API and is currently operational in production.

## Key Architectural Patterns

### Middleware Chain Pattern
Every endpoint follows: Request → CORS → Auth → Rate Limit → Cache Check → Route Handler → Response
- Authentication is mandatory for all endpoints except health checks
- Rate limiting has two tiers: user-based and high-volume (for geometries)
- Caching is selective: 1-hour TTL for expensive operations (zones, geometries, stats)

### Dual Database Strategy
- **Supabase (Main)**: Environmental zones, metadata, structured data
- **Supabase (Thales)**: Extended indicator definitions and schemas
- **InfluxDB**: Real-time time-series data for indicators and measurements
- Route handlers determine data source based on request type

### Error Handling Philosophy
- Always return structured JSON responses with appropriate HTTP codes
- Log all errors through centralized logging system
- Provide clear, user-friendly error messages
- Never expose internal system details in error responses

## Development Patterns

### Code Organization
- Routes are domain-organized: `/routes/zones.ts`, `/routes/indicators.ts`, etc.
- Middleware is cross-cutting: auth, CORS, rate limiting, caching
- Utils contain shared business logic and data transformations
- Types are auto-generated from Supabase schemas

### Authentication Pattern
```typescript
// Every protected route uses this pattern:
if (!req.subscription) {
    return res.status(401).json({
        message: 'Authorization is required to access this resource...'
    });
}
```

### Parameter Validation
- Always validate and sanitize input parameters
- Use consistent error messages for validation failures
- Convert parameters to appropriate types (e.g., toUpperCase() for NUTS IDs)
- Implement regex validation for structured IDs

## Performance Considerations

### Caching Strategy
- Cache expensive operations with 1-hour TTL
- Use cache middleware for zones, geometries, and stats endpoints
- Implement cache-aside pattern: check cache first, populate on miss
- Cache keys should be deterministic based on request parameters

### Rate Limiting
- User-based limits for most endpoints
- High-volume limits for geometry endpoints (they're expensive)
- Rate limiting tied to authentication tokens

### Database Optimization
- Use connection pooling for database clients
- Implement selective field querying to reduce payload size
- Limit results for Swagger UI requests (use `x-swagger-source` header)

## Testing Patterns

### API Testing
- Use Supertest with Jest for endpoint testing
- Test both authenticated and unauthenticated requests
- Verify response structure and status codes
- Use real bearer tokens for integration tests

### Load Testing
- Artillery configuration for different endpoint categories
- Separate test files for different API domains
- Record results to Artillery Cloud for analysis
- Test both normal load and spike scenarios

## Deployment Intelligence

### Environment Configuration
- Use environment variables for all configuration
- Separate development and production Swagger configurations
- Docker deployment with proper volume mounting for logs
- Port 2999 is the standard (configurable via PORT env var)

### Logging Strategy
- Use rotating file streams for log management
- Structure logs for easy parsing and analysis
- Log all errors with sufficient context
- Mount log directory as volume in Docker

## Common Pitfalls to Avoid

1. **Don't bypass authentication**: Every endpoint needs auth except system health
2. **Don't ignore rate limiting**: It's essential for production stability
3. **Don't cache real-time data**: Only cache expensive, relatively static operations
4. **Don't expose internal errors**: Always return user-friendly error messages
5. **Don't forget parameter validation**: Validate all inputs before processing

## Development Workflow

### Type Generation
```bash
# Regenerate types when Supabase schemas change
npx supabase gen types typescript --project-id "afrdxeeulqieluntvmmy" --schema=public,thales > src/types/SupabaseDefinition.d.ts
npx supabase gen types typescript --project-id "nisedejqfskypolnkdie" --schema=thales > src/types/ThalesDefinition.d.ts
```

### Testing Workflow
```bash
pnpm test              # Run unit tests
pnpm test:watch        # Watch mode for development
pnpm load-test:all     # Comprehensive load testing
```

### Code Quality
- Use Prettier for consistent formatting
- TypeScript strict mode is enabled
- Follow existing patterns for consistency

## Production Considerations

### Monitoring
- Health check endpoint provides database connectivity status
- Structured logging enables effective monitoring
- Load testing validates performance characteristics

### Security
- Bearer token authentication on all protected endpoints
- CORS properly configured for cross-origin requests
- Rate limiting prevents abuse
- Environment variables protect sensitive configuration

This system is production-ready and operational. Focus on maintaining existing patterns and performance characteristics when making changes.
