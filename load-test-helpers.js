/**
 * Artillery load test helper functions
 */

// Sample NUTS IDs for testing
const NUTS_IDS = [
  "SE21", "SE22", "SE23", "SI03", "SI04", "SK01", "SK02", "SK03", "SK04", 
  "TR61", "TR52", "TR62", "TR63", "TR71", "TR72", "TRC1", "TRC2", "TRC3", 
  "TR10", "TR82", "TR83", "TR81", "TR90", "TRA1", "TRA2", "TRB1", "TRB2", 
  "TR22", "TR21", "TR31", "TR32", "TR33", "TR41", "TR42", "TR51", "TR52"
];

// Sample indicators for testing
const INDICATORS = [
  "gwp100a", "eco_footprint", "acidification", "eutrophication", "ozone_depletion"
];

// Sample modes for testing
const MODES = ["last", "historical"];

/**
 * Sets default headers for all requests
 */
function setDefaultHeadersIfNeeded(context, events, done) {
  // Set default headers for all requests
  context.vars.headers = {
    'Content-Type': 'application/json'
  };

  // Add Authorization header only if token is provided
  if (context.vars.token) {
    context.vars.headers.Authorization = `Bearer ${context.vars.token}`;
  }

  return done();
}

/**
 * Selects random test values for NUTS IDs, indicators, and modes
 */
function selectRandomTestValues(context, events, done) {
  // Select random values from the predefined arrays
  context.vars.randomNutsId = NUTS_IDS[Math.floor(Math.random() * NUTS_IDS.length)];
  context.vars.randomIndicator = INDICATORS[Math.floor(Math.random() * INDICATORS.length)];
  context.vars.randomMode = MODES[Math.floor(Math.random() * MODES.length)];
  
  // Create a batch of random NUTS IDs
  const numIds = Math.floor(Math.random() * 4) + 2; // 2-5 IDs
  const batchIds = [];
  for (let i = 0; i < numIds; i++) {
    batchIds.push(NUTS_IDS[Math.floor(Math.random() * NUTS_IDS.length)]);
  }
  context.vars.batchNutsIds = [...new Set(batchIds)].join(','); // Remove duplicates and join

  return done();
}

/**
 * Logs response details for debugging
 */
function logResponse(requestParams, response, context, events, done) {
  console.log(`Response status: ${response.statusCode}`);
  if (response.statusCode >= 400) {
    console.log(`Error response: ${JSON.stringify(response.body)}`);
  }
  return done();
}

module.exports = {
  setDefaultHeadersIfNeeded,
  selectRandomTestValues,
  logResponse
}; 