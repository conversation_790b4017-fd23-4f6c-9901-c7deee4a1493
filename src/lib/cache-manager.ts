import { getRedisClient } from './redis';
import { getKeysByPattern, deleteKeysByPattern, getCache } from './redis-utils';

/**
 * Cache Manager - Provides utilities for managing the Redis cache
 */
export class CacheManager {
  /**
   * Get cache statistics
   */
  static async getStats(): Promise<{
    totalKeys: number;
    memoryUsage: string;
    lastSave: string;
    connectedClients: number;
  }> {
    const client = getRedisClient();
    
    try {
      const info = await client.info();
      const infoLines = info.split('\n');
      
      const stats: any = {
        totalKeys: 0,
        memoryUsage: '0B',
        lastSave: 'Never',
        connectedClients: 0
      };
      
      for (const line of infoLines) {
        if (line.startsWith('db0:keys=')) {
          stats.totalKeys = parseInt(line.split('=')[1], 10);
        } else if (line.startsWith('used_memory_human:')) {
          stats.memoryUsage = line.split(':')[1];
        } else if (line.startsWith('rdb_last_save_time:')) {
          const timestamp = parseInt(line.split(':')[1], 10) * 1000;
          stats.lastSave = new Date(timestamp).toISOString();
        } else if (line.startsWith('connected_clients:')) {
          stats.connectedClients = parseInt(line.split(':')[1], 10);
        }
      }
      
      return stats;
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return {
        totalKeys: 0,
        memoryUsage: 'Unknown',
        lastSave: 'Unknown',
        connectedClients: 0
      };
    }
  }
  
  /**
   * Invalidate cache for a specific resource pattern
   */
  static async invalidateResource(resourcePattern: string): Promise<number> {
    const pattern = `cache:*${resourcePattern}*`;
    return await deleteKeysByPattern(pattern);
  }
  
  /**
   * Invalidate all caches
   */
  static async invalidateAll(): Promise<void> {
    const client = getRedisClient();
    await client.flushAll();
  }
  
  /**
   * Get all cache keys
   */
  static async getAllKeys(): Promise<string[]> {
    return await getKeysByPattern('cache:*');
  }
  
  /**
   * Get cache value for a specific key
   */
  static async getCacheValue(key: string): Promise<any> {
    const value = await getCache(key);
    if (!value) return null;
    
    try {
      return JSON.parse(value);
    } catch (error) {
      return value;
    }
  }
  
  /**
   * Get cache keys by prefix
   */
  static async getKeysByPrefix(prefix: string): Promise<string[]> {
    return await getKeysByPattern(`cache:${prefix}*`);
  }
  
  /**
   * Delete cache keys by prefix
   */
  static async deleteKeysByPrefix(prefix: string): Promise<number> {
    return await deleteKeysByPattern(`cache:${prefix}*`);
  }
} 