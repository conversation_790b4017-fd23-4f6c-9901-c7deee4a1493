import { getRedisClient } from './redis';

export const setCache = async (key: string, value: string, expireSeconds?: number) => {
  const client = getRedisClient();
  if (expireSeconds) {
    await client.set(key, value, { EX: expireSeconds });
  } else {
    await client.set(key, value);
  }
};

export const getCache = async (key: string): Promise<string | null> => {
  const client = getRedisClient();
  return await client.get(key);
};

export const deleteCache = async (key: string) => {
  const client = getRedisClient();
  await client.del(key);
};

export const clearAllCache = async () => {
  const client = getRedisClient();
  await client.flushAll();
};

// Get all keys matching a pattern
export const getKeysByPattern = async (pattern: string): Promise<string[]> => {
  const client = getRedisClient();
  return await client.keys(pattern);
};

// Delete all keys matching a pattern
export const deleteKeysByPattern = async (pattern: string): Promise<number> => {
  const client = getRedisClient();
  const keys = await client.keys(pattern);
  
  if (keys.length === 0) {
    return 0;
  }
  
  return await client.del(keys);
};

// Check if a cache is still valid
export const isCacheValid = async (key: string): Promise<boolean> => {
  const client = getRedisClient();
  return await client.exists(key) === 1;
};

// Set cache with metadata
export const setCacheWithMetadata = async (
  key: string, 
  data: any, 
  metadata: Record<string, any> = {}, 
  expireSeconds?: number
) => {
  const cacheData = {
    data,
    metadata: {
      ...metadata,
      timestamp: Date.now()
    }
  };
  
  await setCache(key, JSON.stringify(cacheData), expireSeconds);
};

// Get cache with metadata
export const getCacheWithMetadata = async (key: string): Promise<{ data: any, metadata: any } | null> => {
  const cachedData = await getCache(key);
  
  if (!cachedData) {
    return null;
  }
  
  try {
    return JSON.parse(cachedData);
  } catch (error) {
    console.error('Error parsing cached data:', error);
    return null;
  }
}; 