import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from './types/SupabaseDefinition';
import { Database as ThalesDB } from './types/ThalesDefinition';

// Singleton pattern
let thalesClient: SupabaseClient<ThalesDB> | null = null;
let client: SupabaseClient<Database> | null = null;

export const getSupabaseAdminClient = (): SupabaseClient<Database> => {
    if (!client) {
        const sbUrl = process.env.SUPABASE_URL;
        const sbSecretKey = process.env.SUPABASE_SECRET_KEY;

        if (!sbUrl || !sbSecretKey) {
            throw new Error('Missing SUPABASE_URL or SUPABASE_SECRET_KEY environment variable');
        }

        client = createClient<Database>(sbUrl, sbSecretKey);
    }

    return client;
};

export const getThalesClient = () => {
    if (!thalesClient) {
        const sbUrl = process.env.SUPABASE_THALES_URL;
        const sbSecretKey = process.env.SUPABASE_THALES_SECRET_KEY;
        if (!sbUrl || !sbSecretKey) {
            throw new Error(
                'Missing SUPABASE_THALES_URL or SUPABASE_THALES_SECRET_KEY environment variable'
            );
        }

        thalesClient = createClient<ThalesDB>(sbUrl, sbSecretKey);
    }

    return thalesClient.schema('thales');
};
