import { SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseAdminClient } from '../supabaseClient';

type DataPoint = {
    dt: Date;
    resource_group?: string;
    consumption: number;
    production: number;
};

class DateTimeLRUCache<V> {
    private cache: Map<Date, V>;
    private capacity: number;
    private mostRecentKey: Date | null = null;

    constructor(capacity: number) {
        this.cache = new Map<Date, V>();
        this.capacity = capacity;
    }

    get(key: Date): V | null {
        if (!this.cache.has(key)) {
            return null;
        }
        const value = this.cache.get(key) as V;
        return value;
    }

    put(key: Date, value: V): boolean {
        if (this.cache.has(key)) {
            this.cache.delete(key);
        } else if (this.cache.size >= this.capacity) {
            // Remove oldest entry based on timestamp
            const oldestKey = Array.from(this.cache.keys()).reduce((a, b) =>
                a.getTime() < b.getTime() ? a : b
            );
            this.cache.delete(oldestKey);
        }

        this.cache.set(key, value);

        // Update mostRecentKey based on actual timestamp comparison
        if (this.mostRecentKey === null || key.getTime() > this.mostRecentKey.getTime()) {
            this.mostRecentKey = key;
        }
        return true;
    }

    getMostRecent(): V | null {
        if (this.cache.size === 0) return null;

        const mostRecentKey = Array.from(this.cache.keys()).reduce((a, b) =>
            a.getTime() > b.getTime() ? a : b
        );
        return this.cache.get(mostRecentKey) || null;
    }

    getData(startTime: Date, endTime: Date): V[] {
        return Array.from(this.cache.entries())
            .filter(
                ([timestamp]) =>
                    timestamp.getTime() >= startTime.getTime() &&
                    timestamp.getTime() <= endTime.getTime()
            )
            .sort(([timeA], [timeB]) => timeA.getTime() - timeB.getTime())
            .map(([, value]) => value);
    }

    remove(key: Date): void {
        this.cache.delete(key);
        if (this.mostRecentKey?.getTime() === key.getTime()) {
            const keys = Array.from(this.cache.keys());
            this.mostRecentKey =
                keys.length > 0 ? keys.reduce((a, b) => (a.getTime() > b.getTime() ? a : b)) : null;
        }
    }

    length(): number {
        return this.cache.size;
    }

    contains(key: Date): boolean {
        return this.cache.has(key);
    }

    getMostRecentTimestamp(): Date | null {
        return this.mostRecentKey;
    }

    asList(): Array<[Date, V]> {
        return Array.from(this.cache.entries());
    }

    getAllKeys(): Date[] {
        return Array.from(this.cache.keys());
    }

    updateCacheCapacity(newCapacity: number): void {
        this.capacity = newCapacity;
        const entries = Array.from(this.cache.entries()).sort(
            ([timeA], [timeB]) => timeA.getTime() - timeB.getTime()
        );

        while (this.cache.size > this.capacity && entries.length > 0) {
            const oldestEntry = entries.shift();
            if (oldestEntry) {
                this.cache.delete(oldestEntry[0]);
            }
        }

        if (this.cache.size > 0) {
            const keys = Array.from(this.cache.keys());
            this.mostRecentKey = keys.reduce((a, b) => (a.getTime() > b.getTime() ? a : b));
        } else {
            this.mostRecentKey = null;
        }
    }
}

class DataCache {
    private cache: Map<string, DateTimeLRUCache<DataPoint>>;
    private capacity: number;
    private supabase: SupabaseClient;
    private activeIndicators: Set<string> = new Set();

    constructor() {
        this.capacity = 3600;
        this.cache = new Map();
        this.supabase = getSupabaseAdminClient();
    }

    private getCacheKey(indicator: string, nutsId: string): string {
        return `${indicator}_${nutsId}`;
    }

    get(indicator: string, nutsId: string): DateTimeLRUCache<DataPoint> | undefined {
        const key = this.getCacheKey(indicator, nutsId);
        return this.cache.get(key);
    }

    put(indicator: string, nutsId: string, timestamp: Date, dataPoint: DataPoint): void {
        const key = this.getCacheKey(indicator, nutsId);
        let dtCache = this.cache.get(key);
        if (!dtCache) {
            dtCache = new DateTimeLRUCache<DataPoint>(this.capacity);
            this.cache.set(key, dtCache);
        }
        dtCache.put(timestamp, dataPoint);
    }

    getData(indicator: string, nutsId: string, mode: string): DataPoint[] | null {
        const dtCache = this.get(indicator, nutsId);
        if (!dtCache) {
            return null;
        }

        if (mode === 'last') {
            const data = dtCache.getMostRecent();
            return data ? [data] : null;
        } else if (mode === 'last24h') {
            const now = new Date();
            const cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            return dtCache.getData(cutoff, now);
        }
        return null;
    }

    updateCacheCapacity(newCapacity: number): void {
        this.capacity = newCapacity;
        for (const dtCache of this.cache.values()) {
            dtCache.updateCacheCapacity(newCapacity);
        }
    }

    async getActiveIndicators(): Promise<string[]> {
        try {
            const { data, error } = await this.supabase
                .from('thales.environmental_indicators')
                .select('id_short')
                .order('id_short');

            if (error) throw error;

            const indicators = data.map((row) => row.id_short);
            this.activeIndicators = new Set(indicators);
            return indicators;
        } catch (error) {
            console.error('Error fetching active indicators:', error);
            throw error;
        }
    }

    async initializeLocationsFromDB(supabase: SupabaseClient): Promise<void> {
        try {
            const { data: locations, error } = await supabase
                .from('subscription_locations')
                .select('location_id');

            if (error) throw error;

            const uniqueLocations = new Set(
                (locations as { location_id: string }[]).map((l) => l.location_id)
            );

            console.log('Unique Locations:', uniqueLocations);

            for (const location of uniqueLocations) {
                const indicators = await this.getActiveIndicators();
                for (const indicator of indicators) {
                    const key = this.getCacheKey(indicator, location);
                    if (!this.cache.has(key)) {
                        this.cache.set(key, new DateTimeLRUCache<DataPoint>(this.capacity));
                    }
                }
            }
        } catch (error) {
            console.error('Error initializing location cache:', error);
            throw error;
        }
    }

    /**
     * Get the number of data points that can be stored for the given time period
     * @param days Number of days
     * @returns Number of data points
     */
    static calculatePointsForDays(days: number): number {
        const pointsPerHour = 4; // 15-minute intervals
        const pointsPerDay = 24 * pointsPerHour;
        const totalPoints = days * pointsPerDay;
        const withBuffer = Math.ceil(totalPoints * 1.05); // 5% buffer
        return withBuffer;
    }
}

const dataCache = new DataCache();
export default dataCache;
export type { DataPoint };
