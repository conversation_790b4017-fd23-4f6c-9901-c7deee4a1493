import { InfluxDB, Point } from '@influxdata/influxdb-client';

let influxClient: InfluxDB | null = null;

export const getInfluxClient = (): InfluxDB => {
    if (!influxClient) {
        const url = process.env.INFLUX_URL;
        const token = process.env.INFLUX_TOKEN;

        if (!url || !token) {
            throw new Error('Missing INFLUX_URL or INFLUX_TOKEN environment variable');
        }

        influxClient = new InfluxDB({ url, token });
    }

    return influxClient;
};

export const getInfluxQueryApi = () => {
    const org = process.env.INFLUX_ORG;
    if (!org) {
        throw new Error('Missing INFLUX_ORG environment variable');
    }
    return getInfluxClient().getQueryApi(org);
};
