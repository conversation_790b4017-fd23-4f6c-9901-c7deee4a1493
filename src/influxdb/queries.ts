import dotenv from 'dotenv';
dotenv.config();

const DEFAULT_BUCKET = process.env.INFLUX_DEFAULT_BUCKET || 'emissium';

export interface IndicatorQueryOptions {
  start?: string;
  end?: string;
  filters?: Record<string, string | string[]>;
  getLast?: boolean;
  bucket?: string;
  renameTo?: string;
  returnMeasurement?: boolean;
  limit?: number;
  useSum?: boolean;
}

export const generateIndicatorQuery = (
  indicator: string,
  measurement: string | string[],
  options: IndicatorQueryOptions = {}
): string => {
  const {
    start = '-5d',
    end,
    filters = {},
    getLast = true,
    bucket = DEFAULT_BUCKET,
    renameTo = 'value',
    returnMeasurement = false,
    limit
  } = options;

  // Start building the query
  let query = `from(bucket: "${bucket}")
    |> range(start: ${start}${end ? `, stop: ${end}` : ''})
    |> filter(fn: (r) => `;

  // Handle multiple measurements
  if (Array.isArray(measurement)) {
    const measurementConditions = measurement
      .map((m) => `r["_measurement"] == "${m}"`)
      .join(' or ');
    query += `${measurementConditions})`;
  } else {
    query += `r["_measurement"] == "${measurement}")`;
  }

  // Filter by indicator field
  query += `
    |> filter(fn: (r) => r["indicator"] == "${indicator}" and r["_field"] == "value")`;

  // Add additional filters
  for (const [key, value] of Object.entries(filters)) {
    if (Array.isArray(value) && value.length > 0) {
      if (value.length === 1) {
        query += `\n  |> filter(fn: (r) => r["${key}"] == "${value[0]}")`;
      } else {
        const regexPattern = value.map(v => v.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        query += `\n  |> filter(fn: (r) => r["${key}"] =~ /^(${regexPattern})$/)`;
      }
    } else if (value !== undefined && value !== null) {
      query += `\n  |> filter(fn: (r) => r["${key}"] == "${value}")`;
    }
  }

  if (getLast) {
    query += `\n    |> last()`;
  }

  // Base columns that are always kept
  const baseColumns = ["_time", "_value", "indicator", "nuts_id", "resource_group"];
  
  // Build the keep statement
  query += `
    |> keep(columns: [${baseColumns.map(col => `"${col}"`).join(', ')}${returnMeasurement ? ', "_measurement"' : ''}])
    |> rename(columns: {_value: "${renameTo}", _time: "dt"})`;

  if (!getLast && limit && limit > 0) {
    query += `
    |> sort(columns: ["dt"], desc: false)
    |> tail(n: ${limit})`;
  }
  
  return query;
};

export interface PriceQueryOptions extends Omit<IndicatorQueryOptions, 'filters'> {
  filters?: {
    nuts_id?: string | string[];
    currency?: string;
  };
}

export const generatePriceQuery = (
    measurement: string,
    options: PriceQueryOptions = {}
): string => {
    const {
        start = '-5d',
        end,
        filters = {},
        getLast = true,
        bucket = DEFAULT_BUCKET,
        renameTo = 'price',
        limit
    } = options;

    // Start building the query
    let query = `from(bucket: "${bucket}")
    |> range(start: ${start}${end ? `, stop: ${end}` : ''})
    |> filter(fn: (r) => r["_measurement"] == "${measurement}")`;

    // Filter by field name
    query += `\n    |> filter(fn: (r) => r["_field"] == "value")`;

    // Add filters
    if (filters.nuts_id) {
        if (Array.isArray(filters.nuts_id)) {
            if (filters.nuts_id.length === 1) {
                query += `\n    |> filter(fn: (r) => r["nuts_id"] == "${filters.nuts_id[0]}")`;
            } else {
                const regexPattern = filters.nuts_id.map(v => v.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
                query += `\n    |> filter(fn: (r) => r["nuts_id"] =~ /^(${regexPattern})$/)`;
            }
        } else {
            query += `\n    |> filter(fn: (r) => r["nuts_id"] == "${filters.nuts_id}")`;
        }
    }

    if (filters.currency) {
        query += `\n    |> filter(fn: (r) => r["currency"] == "${filters.currency}")`;
    }

    if (getLast) {
        query += `\n    |> last()`;
    }

    // Base columns that are always kept
    const baseColumns = ["_time", "_value", "nuts_id", "currency"];
    
    // Build the keep statement
    query += `
    |> keep(columns: [${baseColumns.map(col => `"${col}"`).join(', ')}])
    |> rename(columns: {_value: "${renameTo}", _time: "dt"})`;

    if (!getLast && limit && limit > 0) {
        query += `
    |> sort(columns: ["dt"], desc: false)
    |> tail(n: ${limit})`;
    }
    
    return query;
};

export const generateExcludeInactiveIndicatorQuery = (
  indicator: string,
  measurement: string | string[],
  options: IndicatorQueryOptions = {}
): string => {
  const {
    start = '-2d',
    end,
    filters = {},
    getLast = true,
    bucket = DEFAULT_BUCKET,
    renameTo = 'value',
    returnMeasurement = false,
    limit
  } = options;

  // Start building the query
  let query = `from(bucket: "${bucket}")
    |> range(start: ${start}${end ? `, stop: ${end}` : ''})
    |> filter(fn: (r) => `;

  // Handle multiple measurements
  if (Array.isArray(measurement)) {
    const measurementConditions = measurement
      .map((m) => `r["_measurement"] == "${m}"`)
      .join(' or ');
    query += `${measurementConditions})`;
  } else {
    query += `r["_measurement"] == "${measurement}")`;
  }

  // Filter by indicator field
  query += `
    |> filter(fn: (r) => r["indicator"] == "${indicator}" and r["_field"] == "value")`;

  // Add additional filters with negative regex pattern for exclude-inactive
  for (const [key, value] of Object.entries(filters)) {
    if (Array.isArray(value) && value.length > 0) {
      if (value.length === 1) {
        query += `\n  |> filter(fn: (r) => r["${key}"] != "${value[0]}")`;
      } else {
        const regexPattern = value.map(v => v.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
        query += `\n  |> filter(fn: (r) => r["${key}"] !~ /^(${regexPattern})$/)`;
      }
    } else if (value !== undefined && value !== null) {
      query += `\n  |> filter(fn: (r) => r["${key}"] != "${value}")`;
    }
  }

  if (getLast) {
    query += `\n    |> last()`;
  }

  // Base columns that are always kept
  const baseColumns = ["_time", "_value", "indicator", "nuts_id", "resource_group"];
  
  // Build the keep statement
  query += `
    |> keep(columns: [${baseColumns.map(col => `"${col}"`).join(', ')}${returnMeasurement ? ', "_measurement"' : ''}])
    |> rename(columns: {_value: "${renameTo}", _time: "dt"})`;

  if (!getLast && limit && limit > 0) {
    query += `
    |> sort(columns: ["dt"], desc: false)
    |> tail(n: ${limit})`;
  }
  
  return query;
};

export const generateExcludeInactivePriceQuery = (
    measurement: string,
    options: PriceQueryOptions = {}
): string => {
    const {
        start = '-2d',
        end,
        filters = {},
        getLast = true,
        bucket = DEFAULT_BUCKET,
        renameTo = 'price',
        limit
    } = options;

    // Start building the query
    let query = `from(bucket: "${bucket}")
    |> range(start: ${start}${end ? `, stop: ${end}` : ''})
    |> filter(fn: (r) => r["_measurement"] == "${measurement}")`;

    // Filter by field name
    query += `\n    |> filter(fn: (r) => r["_field"] == "value")`;

    // Add filters with negative regex pattern for exclude-inactive
    if (filters.nuts_id) {
        if (Array.isArray(filters.nuts_id)) {
            if (filters.nuts_id.length === 1) {
                query += `\n    |> filter(fn: (r) => r["nuts_id"] != "${filters.nuts_id[0]}")`;
            } else {
                const regexPattern = filters.nuts_id.map(v => v.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
                query += `\n    |> filter(fn: (r) => r["nuts_id"] !~ /^(${regexPattern})$/)`;
            }
        } else {
            query += `\n    |> filter(fn: (r) => r["nuts_id"] != "${filters.nuts_id}")`;
        }
    }

    if (filters.currency) {
        query += `\n    |> filter(fn: (r) => r["currency"] == "${filters.currency}")`;
    }

    if (getLast) {
        query += `\n    |> last()`;
    }

    // Base columns that are always kept
    const baseColumns = ["_time", "_value", "nuts_id", "currency"];
    
    // Build the keep statement
    query += `
    |> keep(columns: [${baseColumns.map(col => `"${col}"`).join(', ')}])
    |> rename(columns: {_value: "${renameTo}", _time: "dt"})`;

    if (!getLast && limit && limit > 0) {
        query += `
    |> sort(columns: ["dt"], desc: false)
    |> tail(n: ${limit})`;
    }
    
    return query;
};
