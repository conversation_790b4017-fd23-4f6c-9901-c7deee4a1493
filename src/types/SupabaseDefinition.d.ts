export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      blogs: {
        Row: {
          created_at: string
          data: string | null
          id: number
        }
        Insert: {
          created_at?: string
          data?: string | null
          id?: number
        }
        Update: {
          created_at?: string
          data?: string | null
          id?: number
        }
        Relationships: []
      }
      careers: {
        Row: {
          created_at: string | null
          department: string | null
          employment_type: string
          id: number
          location: string
          title: string
          updated_at: string | null
          what_we_offer: string[] | null
          what_you_bring: string[] | null
          what_youll_do: string[] | null
          work_arrangement: string
        }
        Insert: {
          created_at?: string | null
          department?: string | null
          employment_type: string
          id?: number
          location: string
          title: string
          updated_at?: string | null
          what_we_offer?: string[] | null
          what_you_bring?: string[] | null
          what_youll_do?: string[] | null
          work_arrangement: string
        }
        Update: {
          created_at?: string | null
          department?: string | null
          employment_type?: string
          id?: number
          location?: string
          title?: string
          updated_at?: string | null
          what_we_offer?: string[] | null
          what_you_bring?: string[] | null
          what_youll_do?: string[] | null
          work_arrangement?: string
        }
        Relationships: []
      }
      discount_codes: {
        Row: {
          created_at: string
          discount_code: string
          discount_percentage: number | null
          duration: number | null
          email: string | null
          max_discount: number | null
          redeemed_at: string | null
        }
        Insert: {
          created_at?: string
          discount_code: string
          discount_percentage?: number | null
          duration?: number | null
          email?: string | null
          max_discount?: number | null
          redeemed_at?: string | null
        }
        Update: {
          created_at?: string
          discount_code?: string
          discount_percentage?: number | null
          duration?: number | null
          email?: string | null
          max_discount?: number | null
          redeemed_at?: string | null
        }
        Relationships: []
      }
      inactive_locations: {
        Row: {
          location_id: string
          user_id: number
        }
        Insert: {
          location_id: string
          user_id: number
        }
        Update: {
          location_id?: string
          user_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "inactive_locations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      locations: {
        Row: {
          id: string
          name: string
        }
        Insert: {
          id: string
          name: string
        }
        Update: {
          id?: string
          name?: string
        }
        Relationships: []
      }
      payment_info: {
        Row: {
          billing_address: Json | null
          company_name: string | null
          created_at: string | null
          id: number
          payment_method_id: string | null
          stripe_customer_id: string | null
          stripe_plan_id: string | null
          stripe_subscription_id: string | null
          tax_id: string | null
          transaction_id: string | null
          user_id: number
        }
        Insert: {
          billing_address?: Json | null
          company_name?: string | null
          created_at?: string | null
          id?: number
          payment_method_id?: string | null
          stripe_customer_id?: string | null
          stripe_plan_id?: string | null
          stripe_subscription_id?: string | null
          tax_id?: string | null
          transaction_id?: string | null
          user_id: number
        }
        Update: {
          billing_address?: Json | null
          company_name?: string | null
          created_at?: string | null
          id?: number
          payment_method_id?: string | null
          stripe_customer_id?: string | null
          stripe_plan_id?: string | null
          stripe_subscription_id?: string | null
          tax_id?: string | null
          transaction_id?: string | null
          user_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "payment_info_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pending_subscription_details: {
        Row: {
          cost_currency: string
          creation_date: string | null
          id: number
          plan_name: string
          subscription_period: string
          total_cost: number
          user_id: number
        }
        Insert: {
          cost_currency: string
          creation_date?: string | null
          id?: number
          plan_name: string
          subscription_period: string
          total_cost: number
          user_id: number
        }
        Update: {
          cost_currency?: string
          creation_date?: string | null
          id?: number
          plan_name?: string
          subscription_period?: string
          total_cost?: number
          user_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "pending_subscription_details_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pending_subscription_locations: {
        Row: {
          location_id: string
          subscription_id: number
        }
        Insert: {
          location_id: string
          subscription_id: number
        }
        Update: {
          location_id?: string
          subscription_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "pending_subscription_locations_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "pending_subscription_details"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          auth_user_id: string | null
          company: string
          country: string
          created_at: string | null
          email: string
          first_name: string
          id: number
          image: string | null
          last_name: string
          position: string | null
          subscription_id: number | null
          updated_at: string | null
        }
        Insert: {
          auth_user_id?: string | null
          company: string
          country: string
          created_at?: string | null
          email: string
          first_name: string
          id?: number
          image?: string | null
          last_name: string
          position?: string | null
          subscription_id?: number | null
          updated_at?: string | null
        }
        Update: {
          auth_user_id?: string | null
          company?: string
          country?: string
          created_at?: string | null
          email?: string
          first_name?: string
          id?: number
          image?: string | null
          last_name?: string
          position?: string | null
          subscription_id?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_profiles_subscription_id"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_costs: {
        Row: {
          id: number
          plan_name: string
          price_in_chf_monthly: number
          price_in_chf_yearly: number
          price_in_eur_monthly: number
          price_in_eur_yearly: number
          price_in_usd_monthly: number
          price_in_usd_yearly: number
        }
        Insert: {
          id?: number
          plan_name: string
          price_in_chf_monthly: number
          price_in_chf_yearly: number
          price_in_eur_monthly: number
          price_in_eur_yearly: number
          price_in_usd_monthly: number
          price_in_usd_yearly: number
        }
        Update: {
          id?: number
          plan_name?: string
          price_in_chf_monthly?: number
          price_in_chf_yearly?: number
          price_in_eur_monthly?: number
          price_in_eur_yearly?: number
          price_in_usd_monthly?: number
          price_in_usd_yearly?: number
        }
        Relationships: [
          {
            foreignKeyName: "subscription_costs_plan_name_fkey"
            columns: ["plan_name"]
            isOneToOne: false
            referencedRelation: "subscription_plans"
            referencedColumns: ["plan_name"]
          },
        ]
      }
      subscription_details: {
        Row: {
          cost_currency: string
          creation_date: string | null
          discount_amount: number | null
          discount_code_id: string | null
          id: number
          is_one_time_purchase: boolean | null
          last_billing_date: string | null
          next_billing_date: string | null
          plan_name: string
          start_date: string | null
          subscription_period: string
          total_cost: number
          user_id: number
        }
        Insert: {
          cost_currency: string
          creation_date?: string | null
          discount_amount?: number | null
          discount_code_id?: string | null
          id?: number
          is_one_time_purchase?: boolean | null
          last_billing_date?: string | null
          next_billing_date?: string | null
          plan_name: string
          start_date?: string | null
          subscription_period: string
          total_cost: number
          user_id: number
        }
        Update: {
          cost_currency?: string
          creation_date?: string | null
          discount_amount?: number | null
          discount_code_id?: string | null
          id?: number
          is_one_time_purchase?: boolean | null
          last_billing_date?: string | null
          next_billing_date?: string | null
          plan_name?: string
          start_date?: string | null
          subscription_period?: string
          total_cost?: number
          user_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_subscription_details_user_id"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_details_discount_code_id_fkey"
            columns: ["discount_code_id"]
            isOneToOne: false
            referencedRelation: "discount_codes"
            referencedColumns: ["discount_code"]
          },
          {
            foreignKeyName: "subscription_details_plan_name_fkey"
            columns: ["plan_name"]
            isOneToOne: false
            referencedRelation: "subscription_plans"
            referencedColumns: ["plan_name"]
          },
        ]
      }
      subscription_locations: {
        Row: {
          location_data_year: number | null
          location_id: string
          pending_removal: boolean | null
          subscription_id: number
        }
        Insert: {
          location_data_year?: number | null
          location_id: string
          pending_removal?: boolean | null
          subscription_id: number
        }
        Update: {
          location_data_year?: number | null
          location_id?: string
          pending_removal?: boolean | null
          subscription_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_subscription_locations_subscription_id"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_locations_location_id_fkey1"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_locations_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_plans: {
        Row: {
          base_RL: number | null
          created_at: string
          description: string | null
          display_name: string | null
          features: string[] | null
          plan_name: string
          product_id: string | null
          RL_per_location: number | null
          tag: Database["public"]["Enums"]["plan_tag"] | null
        }
        Insert: {
          base_RL?: number | null
          created_at?: string
          description?: string | null
          display_name?: string | null
          features?: string[] | null
          plan_name: string
          product_id?: string | null
          RL_per_location?: number | null
          tag?: Database["public"]["Enums"]["plan_tag"] | null
        }
        Update: {
          base_RL?: number | null
          created_at?: string
          description?: string | null
          display_name?: string | null
          features?: string[] | null
          plan_name?: string
          product_id?: string | null
          RL_per_location?: number | null
          tag?: Database["public"]["Enums"]["plan_tag"] | null
        }
        Relationships: []
      }
      user_api_keys: {
        Row: {
          api_key: string | null
          creation_date: string | null
          id: number
          status: Database["public"]["Enums"]["api_key_status"]
          subscription_id: number
        }
        Insert: {
          api_key?: string | null
          creation_date?: string | null
          id?: number
          status?: Database["public"]["Enums"]["api_key_status"]
          subscription_id: number
        }
        Update: {
          api_key?: string | null
          creation_date?: string | null
          id?: number
          status?: Database["public"]["Enums"]["api_key_status"]
          subscription_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_api_keys_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
        ]
      }
      user_feedback: {
        Row: {
          delete_acc_feedback: string | null
          id: number
          unsubscribe_feedback: string | null
          user_id: number | null
        }
        Insert: {
          delete_acc_feedback?: string | null
          id?: number
          unsubscribe_feedback?: string | null
          user_id?: number | null
        }
        Update: {
          delete_acc_feedback?: string | null
          id?: number
          unsubscribe_feedback?: string | null
          user_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "user_feedback_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_receipts: {
        Row: {
          created_at: string | null
          id: number
          receipt_link: string
          receipt_number: number
          user_id: number | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          receipt_link: string
          receipt_number?: number
          user_id?: number | null
        }
        Update: {
          created_at?: string | null
          id?: number
          receipt_link?: string
          receipt_number?: number
          user_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "user_receipts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      fetch_active_subscription_data: {
        Args: {
          api_token: string
        }
        Returns: Database["public"]["CompositeTypes"]["subscription_record_auth_middleware"]
      }
      find_zone_details: {
        Args: {
          input_zone_key: string
        }
        Returns: Database["public"]["CompositeTypes"]["zone_details_with_children"]
      }
      find_zone_details_geometry: {
        Args: {
          input_zone_key: string
        }
        Returns: Database["public"]["CompositeTypes"]["zone_details_geometry_type"]
      }
      generate_unique_random_id: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_active_bidding_zones_with_entsoe: {
        Args: Record<PropertyKey, never>
        Returns: {
          zone_key: string
          code: string
        }[]
      }
      get_subscription_id: {
        Args: {
          p_user_id: number
        }
        Returns: number
      }
    }
    Enums: {
      api_key_status: "active" | "revoked"
      api_limit_per_minute: "Unlimited" | "300" | "100"
      geometry_type_enum:
        | "generator"
        | "bus"
        | "link"
        | "line"
        | "inactive-countries"
        | "active-countries"
      nuts_zone: "0" | "1" | "2" | "3"
      plan_tag: "Most popular" | "Best value"
      share_carbon_emission_per_source_type_enum: "consumption" | "generation"
    }
    CompositeTypes: {
      subscription_record_auth_middleware: {
        subscription_id: number | null
        details_id: number | null
        plan_name: string | null
        user_id: number | null
        subscription_period: string | null
        locations: string[] | null
      }
      zone_details_geometry_type: {
        zone_key: string | null
        name: string | null
        is_active: boolean | null
        child_zones: string[] | null
        geometry: unknown | null
      }
      zone_details_type: {
        zone_key: string | null
        name: string | null
        is_active: boolean | null
        geometry: unknown | null
      }
      zone_details_with_children: {
        zone_key: string | null
        name: string | null
        is_active: boolean | null
        child_zones: string[] | null
      }
    }
  }
  thales: {
    Tables: {
      environmental_indicators: {
        Row: {
          category: string
          comments: string | null
          display_name: string
          display_name_short: string
          id: string
          id_short: string
          indicator: string
          indicator_extended: string
          methodology: string
          model_type: string
          system_model: string
          units: string
        }
        Insert: {
          category: string
          comments?: string | null
          display_name: string
          display_name_short: string
          id: string
          id_short: string
          indicator: string
          indicator_extended: string
          methodology: string
          model_type: string
          system_model: string
          units: string
        }
        Update: {
          category?: string
          comments?: string | null
          display_name?: string
          display_name_short?: string
          id?: string
          id_short?: string
          indicator?: string
          indicator_extended?: string
          methodology?: string
          model_type?: string
          system_model?: string
          units?: string
        }
        Relationships: []
      }
      nuts_0_zones: {
        Row: {
          macro_area: string[]
          nuts_id: string
          timezone: string
        }
        Insert: {
          macro_area: string[]
          nuts_id: string
          timezone: string
        }
        Update: {
          macro_area?: string[]
          nuts_id?: string
          timezone?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_0_zones_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_1_zones: {
        Row: {
          nuts_id: string
          parent_n0: string
        }
        Insert: {
          nuts_id: string
          parent_n0: string
        }
        Update: {
          nuts_id?: string
          parent_n0?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_1_zones_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_1_zones_parent_n0_fkey"
            columns: ["parent_n0"]
            isOneToOne: false
            referencedRelation: "nuts_0_zones"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_2_zones: {
        Row: {
          nuts_id: string
          parent_n0: string
          parent_n1: string
        }
        Insert: {
          nuts_id: string
          parent_n0: string
          parent_n1: string
        }
        Update: {
          nuts_id?: string
          parent_n0?: string
          parent_n1?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_2_zones_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_2_zones_parent_n0_fkey"
            columns: ["parent_n0"]
            isOneToOne: false
            referencedRelation: "nuts_0_zones"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_2_zones_parent_n1_fkey"
            columns: ["parent_n1"]
            isOneToOne: false
            referencedRelation: "nuts_1_zones"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_3_zones: {
        Row: {
          nuts_id: string
          parent_n0: string
          parent_n1: string
          parent_n2: string
        }
        Insert: {
          nuts_id: string
          parent_n0: string
          parent_n1: string
          parent_n2: string
        }
        Update: {
          nuts_id?: string
          parent_n0?: string
          parent_n1?: string
          parent_n2?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_3_zones_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_3_zones_parent_n0_fkey"
            columns: ["parent_n0"]
            isOneToOne: false
            referencedRelation: "nuts_0_zones"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_3_zones_parent_n1_fkey"
            columns: ["parent_n1"]
            isOneToOne: false
            referencedRelation: "nuts_1_zones"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_3_zones_parent_n2_fkey"
            columns: ["parent_n2"]
            isOneToOne: false
            referencedRelation: "nuts_2_zones"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_geometries: {
        Row: {
          geometry: unknown
          nuts_id: string
        }
        Insert: {
          geometry: unknown
          nuts_id: string
        }
        Update: {
          geometry?: unknown
          nuts_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_geometries_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_info: {
        Row: {
          is_active: boolean
          name: string
          nuts: Database["public"]["Enums"]["nuts_zone"]
          nuts_id: string
        }
        Insert: {
          is_active?: boolean
          name: string
          nuts: Database["public"]["Enums"]["nuts_zone"]
          nuts_id: string
        }
        Update: {
          is_active?: boolean
          name?: string
          nuts?: Database["public"]["Enums"]["nuts_zone"]
          nuts_id?: string
        }
        Relationships: []
      }
      nuts_metadata: {
        Row: {
          created_at: string
          gdp_mio_eur: string | null
          nuts_id: string
          population: string | null
          year: string | null
        }
        Insert: {
          created_at?: string
          gdp_mio_eur?: string | null
          nuts_id: string
          population?: string | null
          year?: string | null
        }
        Update: {
          created_at?: string
          gdp_mio_eur?: string | null
          nuts_id?: string
          population?: string | null
          year?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      execute_sql: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      get_active_bidding_neighbors: {
        Args: {
          zone_key_input: string
        }
        Returns: {
          bidding_zone: string
          neighbour_zone: string
        }[]
      }
      get_active_bidding_zones_with_entsoe: {
        Args: Record<PropertyKey, never>
        Returns: {
          zone_key: string
          code: string
        }[]
      }
      get_all_active_neighbors: {
        Args: Record<PropertyKey, never>
        Returns: {
          bidding_zone: string
          neighbour_zone: string
        }[]
      }
      get_nuts_details_with_children: {
        Args: {
          input_nuts_id: string
        }
        Returns: Database["thales"]["CompositeTypes"]["nuts_details_with_children"]
      }
      get_nuts_details_with_detailed_children: {
        Args: {
          input_nuts_id: string
        }
        Returns: Database["thales"]["CompositeTypes"]["nuts_details_with_detailed_children"]
      }
      get_nuts_info_with_parent: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          is_active: boolean
          nuts: Database["public"]["Enums"]["nuts_zone"]
          parent: string
        }[]
      }
      get_zone_details: {
        Args: {
          input_zone_key: string
        }
        Returns: Database["thales"]["CompositeTypes"]["get_zone_details"]
      }
    }
    Enums: {
      nuts_zone: "0" | "1" | "2" | "3"
    }
    CompositeTypes: {
      child_details: {
        nuts_id: string | null
        name: string | null
        is_active: boolean | null
      }
      get_zone_details: {
        zone_key: string | null
        name: string | null
        is_active: boolean | null
        children: string[] | null
      }
      nuts_details_with_children: {
        nuts_id: string | null
        name: string | null
        is_active: boolean | null
        nuts_level: string | null
        children: string[] | null
      }
      nuts_details_with_detailed_children: {
        nuts_id: string | null
        name: string | null
        is_active: boolean | null
        nuts_level: string | null
        children: Database["thales"]["CompositeTypes"]["child_details"][] | null
      }
      zone_details_with_children: {
        zone_key: string | null
        name: string | null
        is_active: boolean | null
        children: string[] | null
      }
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
