export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  thales: {
    Tables: {
      environmental_indicators: {
        Row: {
          category: string
          comments: string | null
          display_name: string
          display_name_short: string
          id: string
          id_short: string
          indicator: string
          indicator_extended: string
          methodology: string
          model_type: string
          system_model: string
          units: string
        }
        Insert: {
          category: string
          comments?: string | null
          display_name: string
          display_name_short: string
          id: string
          id_short: string
          indicator: string
          indicator_extended: string
          methodology: string
          model_type: string
          system_model: string
          units: string
        }
        Update: {
          category?: string
          comments?: string | null
          display_name?: string
          display_name_short?: string
          id?: string
          id_short?: string
          indicator?: string
          indicator_extended?: string
          methodology?: string
          model_type?: string
          system_model?: string
          units?: string
        }
        Relationships: []
      }
      nuts_0_zones: {
        Row: {
          macro_area: string[]
          nuts_id: string
          timezone: string
        }
        Insert: {
          macro_area: string[]
          nuts_id: string
          timezone: string
        }
        Update: {
          macro_area?: string[]
          nuts_id?: string
          timezone?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_0_zones_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_1_zones: {
        Row: {
          nuts_id: string
          parent_n0: string
        }
        Insert: {
          nuts_id: string
          parent_n0: string
        }
        Update: {
          nuts_id?: string
          parent_n0?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_1_zones_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_1_zones_parent_n0_fkey"
            columns: ["parent_n0"]
            isOneToOne: false
            referencedRelation: "nuts_0_zones"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_2_zones: {
        Row: {
          nuts_id: string
          parent_n0: string
          parent_n1: string
        }
        Insert: {
          nuts_id: string
          parent_n0: string
          parent_n1: string
        }
        Update: {
          nuts_id?: string
          parent_n0?: string
          parent_n1?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_2_zones_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_2_zones_parent_n0_fkey"
            columns: ["parent_n0"]
            isOneToOne: false
            referencedRelation: "nuts_0_zones"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_2_zones_parent_n1_fkey"
            columns: ["parent_n1"]
            isOneToOne: false
            referencedRelation: "nuts_1_zones"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_3_zones: {
        Row: {
          nuts_id: string
          parent_n0: string
          parent_n1: string
          parent_n2: string
        }
        Insert: {
          nuts_id: string
          parent_n0: string
          parent_n1: string
          parent_n2: string
        }
        Update: {
          nuts_id?: string
          parent_n0?: string
          parent_n1?: string
          parent_n2?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_3_zones_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_3_zones_parent_n0_fkey"
            columns: ["parent_n0"]
            isOneToOne: false
            referencedRelation: "nuts_0_zones"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_3_zones_parent_n1_fkey"
            columns: ["parent_n1"]
            isOneToOne: false
            referencedRelation: "nuts_1_zones"
            referencedColumns: ["nuts_id"]
          },
          {
            foreignKeyName: "nuts_3_zones_parent_n2_fkey"
            columns: ["parent_n2"]
            isOneToOne: false
            referencedRelation: "nuts_2_zones"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_geometries: {
        Row: {
          geometry: unknown
          nuts_id: string
        }
        Insert: {
          geometry: unknown
          nuts_id: string
        }
        Update: {
          geometry?: unknown
          nuts_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuts_geometries_nuts_id_fkey"
            columns: ["nuts_id"]
            isOneToOne: true
            referencedRelation: "nuts_info"
            referencedColumns: ["nuts_id"]
          },
        ]
      }
      nuts_info: {
        Row: {
          is_active: boolean
          name: string
          nuts: "0" | "1" | "2" | "3"
          nuts_id: string
        }
        Insert: {
          is_active?: boolean
          name: string
          nuts: "0" | "1" | "2" | "3"
          nuts_id: string
        }
        Update: {
          is_active?: boolean
          name?: string
          nuts?: "0" | "1" | "2" | "3"
          nuts_id?: string
        }
        Relationships: []
      }
      nuts_metadata: {
        Row: {
          created_at: string
          gdp_mio_eur: string | null
          nuts_id: string
          population: string | null
          year: string | null
        }
        Insert: {
          created_at?: string
          gdp_mio_eur?: string | null
          nuts_id: string
          population?: string | null
          year?: string | null
        }
        Update: {
          created_at?: string
          gdp_mio_eur?: string | null
          nuts_id?: string
          population?: string | null
          year?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      execute_sql: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      get_active_bidding_neighbors: {
        Args: {
          zone_key_input: string
        }
        Returns: {
          bidding_zone: string
          neighbour_zone: string
        }[]
      }
      get_active_bidding_zones_with_entsoe: {
        Args: Record<PropertyKey, never>
        Returns: {
          zone_key: string
          code: string
        }[]
      }
      get_all_active_neighbors: {
        Args: Record<PropertyKey, never>
        Returns: {
          bidding_zone: string
          neighbour_zone: string
        }[]
      }
      get_nuts_details_with_children: {
        Args: {
          input_nuts_id: string
        }
        Returns: Database["thales"]["CompositeTypes"]["nuts_details_with_children"]
      }
      get_nuts_details_with_detailed_children: {
        Args: {
          input_nuts_id: string
        }
        Returns: Database["thales"]["CompositeTypes"]["nuts_details_with_detailed_children"]
      }
      get_nuts_info_with_parent: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          is_active: boolean
          nuts: "0" | "1" | "2" | "3"
          parent: string
        }[]
      }
      get_zone_details: {
        Args: {
          input_zone_key: string
        }
        Returns: Database["thales"]["CompositeTypes"]["get_zone_details"]
      }
    }
    Enums: {
      nuts_zone: "0" | "1" | "2" | "3"
    }
    CompositeTypes: {
      child_details: {
        nuts_id: string | null
        name: string | null
        is_active: boolean | null
      }
      get_zone_details: {
        zone_key: string | null
        name: string | null
        is_active: boolean | null
        children: string[] | null
      }
      nuts_details_with_children: {
        nuts_id: string | null
        name: string | null
        is_active: boolean | null
        nuts_level: string | null
        children: string[] | null
      }
      nuts_details_with_detailed_children: {
        nuts_id: string | null
        name: string | null
        is_active: boolean | null
        nuts_level: string | null
        children: Database["thales"]["CompositeTypes"]["child_details"][] | null
      }
      zone_details_with_children: {
        zone_key: string | null
        name: string | null
        is_active: boolean | null
        children: string[] | null
      }
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
