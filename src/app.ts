import express from 'express';
import dotenv from 'dotenv';
import { authenticateToken } from './middleware/auth';
import { setupLogging } from './logging';
import { handleCors } from './middleware/handleCors';
import heartbeat from './routes/system/heartbeat';
import { createUserBasedRateLimiter, createHighBasedRateLimiter } from './middleware/rateLimits';
import routeZones from './routes/zones';
import routeGeometriesPro from './routes/geometriesPro';
import routeIndicators from './routes/indicators';
import routeStats from './routes/stats/statsRoutes';
import setupSwagger from './swagger';
import { cacheMiddleware, serveCachedResponse } from './middleware/cacheMiddleware';
import routeUpdateCache from './routes/updateCache';
import routeCalculate from './routes/calculate';
import routePower from './routes/power';
import routePrice from './routes/price';
import routePowerBreakdown from './routes/powerBreakDown';

dotenv.config();

const app = express();
const port = process.env.PORT || 2999;

app.use(handleCors);
app.use(express.json());
setupLogging(app);

app.use('/api/v1/system', authenticateToken(), createUserBasedRateLimiter(), heartbeat);
app.use(
    '/api/v1/zones/',
    cacheMiddleware(1 * 60 * 60),
    authenticateToken(),
    createUserBasedRateLimiter(),
    serveCachedResponse,
    routeZones
);
app.use(
    '/api/v1/stats/',
    cacheMiddleware(1 * 60 * 60),
    authenticateToken(),
    createUserBasedRateLimiter(),
    serveCachedResponse,
    routeStats
);
app.use('/api/v1/indicators', authenticateToken(), createUserBasedRateLimiter(), routeIndicators);
app.use('/api/v1/impacts', authenticateToken(), createUserBasedRateLimiter(), routeIndicators);
app.use('/api/v1/power_breakdown', authenticateToken(), createUserBasedRateLimiter(), routePowerBreakdown);
app.use(
    '/api/v1/geometries/',
    cacheMiddleware(1 * 60 * 60),
    authenticateToken(),
    createHighBasedRateLimiter(),
    serveCachedResponse,
    routeGeometriesPro
);
app.use('/api/v1/update-indicators-cache', authenticateToken(), routeUpdateCache);

// New endpoints
app.use('/api/v1/calculate', authenticateToken(), createUserBasedRateLimiter(), routeCalculate);
app.use('/api/v1/power', authenticateToken(), createUserBasedRateLimiter(), routePower);
app.use('/api/v1/price', authenticateToken(), createUserBasedRateLimiter(), routePrice);
setupSwagger(app);

export function startServer(customPort?: number) {
    return new Promise((resolve) => {
        const serverPort = customPort !== undefined ? customPort : port;
        const server = app.listen(serverPort, () => {
            console.log(`Server is running at http://127.0.0.1:${serverPort}`);
            resolve(server);
        });
    });
}

if (require.main === module) {
    startServer();
}

export default app;
