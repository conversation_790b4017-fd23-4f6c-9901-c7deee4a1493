import express from 'express';
import dotenv from 'dotenv';
import { authenticateToken } from './middleware/auth';
import { setupLogging } from './logging';
import { handleCors } from './middleware/handleCors';
import heartbeat from './routes/system/heartbeat';
import { createUserBasedRateLimiter, createHighBasedRateLimiter } from './middleware/rateLimits';
import routeZones from './routes/zones';
import routeGeometriesPro from './routes/geometriesPro';
import routeIndicators from './routes/indicators';
import routeStats from './routes/stats/statsRoutes';
import setupSwagger from './swagger';
import { advancedCacheMiddleware, updateCacheOnChange } from './middleware/advancedCacheMiddleware';
import routeUpdateCache from './routes/updateCache';
import routeCalculate from './routes/calculate';
import routePower from './routes/power';
import routePrice from './routes/price';
import routePowerBreakdown from './routes/powerBreakDown';
import { connectRedis } from './lib/redis';
import redisExample from './routes/redis-example';
import cacheManagement from './routes/cache-management';

dotenv.config();

const app = express();
const port = process.env.PORT || 2999;

// Initialize Redis connection
connectRedis().catch(console.error);

app.use(handleCors);
app.use(express.json());
setupLogging(app);

// Apply cache invalidation middleware to all routes
app.use(updateCacheOnChange());

// Apply advanced caching to all GET routes
app.use(advancedCacheMiddleware(15 * 60)); // 15 minutes default TTL

app.use('/api/v1/system', authenticateToken(), createUserBasedRateLimiter(), heartbeat);
app.use(
    '/api/v1/zones/',
    authenticateToken(),
    createUserBasedRateLimiter(),
    routeZones
);
app.use(
    '/api/v1/stats/',
    authenticateToken(),
    createUserBasedRateLimiter(),
    routeStats
);
app.use('/api/v1/indicators', authenticateToken(), createUserBasedRateLimiter(), routeIndicators);
app.use('/api/v1/impacts', authenticateToken(), createUserBasedRateLimiter(), routeIndicators);
app.use('/api/v1/power_breakdown', authenticateToken(), createUserBasedRateLimiter(), routePowerBreakdown);
app.use(
    '/api/v1/geometries/',
    authenticateToken(),
    createHighBasedRateLimiter(),
    routeGeometriesPro
);
app.use('/api/v1/update-indicators-cache', authenticateToken(), routeUpdateCache);

// New endpoints
app.use('/api/v1/calculate', authenticateToken(), createUserBasedRateLimiter(), routeCalculate);
app.use('/api/v1/power', authenticateToken(), createUserBasedRateLimiter(), routePower);
app.use('/api/v1/price', authenticateToken(), createUserBasedRateLimiter(), routePrice);

// Redis example routes
app.use('/api/v1/redis', redisExample);

// Cache management routes
app.use('/api/v1/cache', authenticateToken(), cacheManagement);

setupSwagger(app);

export function startServer(customPort?: number) {
    return new Promise(async (resolve) => {
        // Ensure Redis is connected before starting the server
        try {
            await connectRedis();
            console.log('Redis connection established');
        } catch (error) {
            console.error('Failed to connect to Redis:', error);
        }

        const serverPort = customPort !== undefined ? customPort : port;
        const server = app.listen(serverPort, () => {
            console.log(`Server is running at http://127.0.0.1:${serverPort}`);
            resolve(server);
        });
    });
}

if (require.main === module) {
    startServer();
}

export default app;
