// Types
export type Mode = 'last' | 'last24h' | 'historical';
export type PowerType = 'consumption' | 'production';

// Interfaces
export interface ValidationError {
    status: number;
    body: {
        message: string;
    };
}

export interface DataPoint {
    dt: string;
    value?: number;
    consumption?: number;
    production?: number;
    resource_group?: string;
    value_per_capita?: number;
}

export interface ResponseData {
    id: string;
    indicator: string;
    units: string;
    mode: Mode;
    nuts_id: string | string[];
    data: DataPoint[] | Record<string, DataPoint[]>;
    type?: PowerType;
}