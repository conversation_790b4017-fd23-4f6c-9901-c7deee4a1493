import { getSupabaseAdminClient, getThalesClient } from '../supabaseClient';
import { getInfluxQueryApi } from '../influxdb/influxClient';
import { Database } from '../types/SupabaseDefinition';

const CACHE_DURATION_MS = 15 * 60 * 1000; // 15 minutes

interface CacheEntry {
    timestamp: number;
    data: string[];
}
const influxCache: Record<string, CacheEntry> = {};

export const getMeasurementFields = async (measurement: string) => {
    const now = Date.now();
    const cacheEntry = influxCache[measurement];

    // Check if the cache is valid
    if (cacheEntry && now - cacheEntry.timestamp < CACHE_DURATION_MS) {
        return cacheEntry.data;
    }

    try {
        const influx = getInfluxQueryApi();

        const query = `import "influxdata/influxdb/schema"
            schema.tagValues(
                bucket: "emissium",
                predicate: (r) => r["_measurement"] == "${measurement}",
                tag: "indicator"
            )`;

        const indicatorValues = await influx.collectRows(query);

        const fields = (indicatorValues.map((row: any) => row._value) as string[]) || [];
        influxCache[measurement] = { timestamp: now, data: fields };

        return fields;
    } catch (error) {
        console.error('Error retrieving measurement indicators:', error);
        return [] as string[];
    }
};

export interface ThalesIndicatorData {
    id: string;
    id_short: string;
    methodology: string;
    units: string;
    category: string;
    system_model: string;
    model_type: string;
    display_name_short: string;
    indicator_extended: string;
  }

interface ThalesIndicatorCache {
    timestamp: number;
    data: ThalesIndicatorData[]; // Adjust the type based on the actual data structure
}

let thalesCache: ThalesIndicatorCache | null = null;

export const getThalesIndicators = async () => {
    const now = Date.now();

    // Check if the cache is valid
    if (thalesCache && now - thalesCache.timestamp < CACHE_DURATION_MS) {
        return thalesCache.data;
    }

    try {
        const { data, error } = await getThalesClient()
            .from('environmental_indicators')
            .select(
                'id, id_short, methodology,units, category, system_model, model_type, display_name_short, indicator_extended'
            )
            .order('id');

        if (error) {
            throw new Error(error.message);
        }
        // Update the cache
        thalesCache = { timestamp: now, data };
        return data;
    } catch (error) {
        console.error(error);
        return [] as ThalesIndicatorData[]; // Adjust the type based on the actual data structure
    }
};

interface SubIndicatorData {
    indicator_id: string;
    sub_plan: string;
}
interface SubIndicatorCache {
    timestamp: number;
    data: SubIndicatorData[]; // Adjust the type based on the actual data structure
}

let subIndicatorCache: SubIndicatorCache | null = null;

// below func has been commented out since we provide all the indicators to all the plans, remove it if not needed later
// export const getSubIndicators = async (plan: string | null) => {
//     const now = Date.now();

//     // Check if the cache is valid
//     if (subIndicatorCache && now - subIndicatorCache.timestamp < CACHE_DURATION_MS) {
//         return subIndicatorCache.data.filter((x) => x.sub_plan === plan).map((x) => x.indicator_id);
//     }

//     try {
//         const { data, error } = await getSupabaseAdminClient()
//             .from('sub_indicator_map')
//             .select('*');

//         if (error) {
//             throw new Error(error.message);
//         }
//         // Update the cache
//         subIndicatorCache = { timestamp: now, data };
//         return data.filter((x) => x.sub_plan === plan).map((x) => x.indicator_id);
//     } catch (error) {
//         console.error(error);
//         return []; // Adjust the type based on the actual data structure
//     }
// };

interface SubPlanCache {
    timestamp: number;
    data: Database['public']['Tables']['subscription_plans']['Row'][]; // Adjust the type based on the actual data structure
}
let subPlanCache: SubPlanCache | null = null;

export const getSubPlan = async (plan: string | null) => {
    const now = Date.now();

    // Check if the cache is valid
    if (subPlanCache && now - subPlanCache.timestamp < CACHE_DURATION_MS) {
        return subPlanCache.data.find((x) => x.plan_name === plan);
    }

    try {
        const { data, error } = await getSupabaseAdminClient()
            .from('subscription_plans')
            .select('*');

        if (error) {
            throw new Error(error.message);
        }
        // Update the cache
        subPlanCache = { timestamp: now, data };
        return data.find((x) => x.plan_name === plan);
    } catch (error) {
        console.error(error);
        return undefined; // Adjust the type based on the actual data structure
    }
};
