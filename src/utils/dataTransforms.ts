export interface InfluxDataPoint {
    [key: string]: any;
    dt: string;
    nuts_id: string;
}

interface TransformedDataPoint {
    dt: string;
    consumption: number | null;
    production: number | null;
}

export interface GroupedData {
    [key: string]: Array<{ [key: string]: any }>;
}

export function groupByKey(data: InfluxDataPoint[], groupByKey: string): GroupedData {
    return data.reduce((acc: GroupedData, curr: InfluxDataPoint) => {
        const groupKeyValue = curr[groupByKey];
        if (!acc[groupKeyValue]) {
            acc[groupKeyValue] = [];
        }
        const retainedData = Object.keys(curr).reduce(
            (obj, key) => {
                if (key !== groupByKey) {
                    obj[key] = curr[key];
                }
                return obj;
            },
            {} as { [key: string]: any }
        );

        acc[groupKeyValue].push(retainedData);
        return acc;
    }, {});
}

// export function transformConsProdData(data: InfluxDataPoint[]): TransformedDataPoint[] {
//     const transformedMap: Record<string, TransformedDataPoint> = {};

//     data.forEach((point) => {
//         const { datetime, _measurement, value } = point;
//         if (!transformedMap[datetime]) {
//             transformedMap[datetime] = { datetime, production: null, consumption: null };
//         }

//         if (_measurement.startsWith('cons')) {
//             transformedMap[datetime].production = value;
//         } else if (_measurement.startsWith('prod')) {
//             transformedMap[datetime].consumption = value;
//         }
//     });

//     return Object.values(transformedMap);
// }

// dataTransforms.ts
export function transformConsProdData(data: InfluxDataPoint[]): Array<{
    dt: string;
    resource_group: string;
    consumption?: number | null;
    production?: number | null;
}> {
    // Group by (dt + resource_group), so we don't lose the resource group detail
    const transformedMap: Record<
        string,
        {
            dt: string;
            resource_group: string;
            consumption: number | null;
            production: number | null;
        }
    > = {};

    data.forEach((point) => {
        const { dt, resource_group, _measurement, value } = point;
        const key = `${dt}-${resource_group}`;

        if (!transformedMap[key]) {
            transformedMap[key] = {
                dt,
                resource_group,
                consumption: null,
                production: null
            };
        }

        if (_measurement === 'consumption') {
            transformedMap[key].consumption = value;
        } else if (_measurement === 'production') {
            transformedMap[key].production = value;
        }
    });

    return Object.values(transformedMap);
}
