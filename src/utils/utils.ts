import { getInfluxQueryApi } from "../influxdb/influxClient";
import { generateIndicatorQuery, generatePriceQuery, IndicatorQueryOptions, PriceQueryOptions } from "../influxdb/queries";
import { AuthRequest } from "../middleware/auth";
import { groupByKey, InfluxDataPoint } from "./dataTransforms";
import { DataPoint, Mode, ResponseData, ValidationError } from "./types";

export function chunkArray<T>(arr: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < arr.length; i += size) {
        chunks.push(arr.slice(i, i + size));
    }
    return chunks;
}

export function transformResourceGroups(dataPoints: any[], includeProduction?: boolean) {
    const consumption: Record<string, number> = {};
    const production: Record<string, number> = {};

    dataPoints.forEach((dp) => {
        if (includeProduction) {
            if (dp.consumption !== null && dp.consumption !== undefined) {
                consumption[dp.resource_group] = Number(dp.consumption.toFixed(4));
            }
            if (dp.production !== null && dp.production !== undefined) {
                production[dp.resource_group] = Number(dp.production.toFixed(4));
            }
        } else {
            if (dp.consumption !== null && dp.consumption !== undefined) {
                consumption[dp.resource_group] = Number(dp.consumption.toFixed(4));
            }
        }
    });

    const result: any = {};
    if (Object.keys(consumption).length > 0) {
        result.consumption = consumption;
    }
    if (includeProduction && Object.keys(production).length > 0) {
        result.production = production;
    }

    return result;
}

export function parseAndFormatDate(dt: string | Date): string {
    if (dt instanceof Date) {
        return dt.toISOString().replace(/\.\d+Z$/, 'Z');
    }
    const validatedDate = validateDate(dt);
    if (!validatedDate) {
        return String(dt);
    }
    return validatedDate.toISOString().replace(/\.\d+Z$/, 'Z');
}

export function validateDate(dateStr: string): Date | null {
    const iso8601Regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/;
    if (typeof dateStr !== 'string' || !iso8601Regex.test(dateStr)) {
        return null;
    }
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
}

// Data Fetching Functions
export async function fetchChunkedData(
    influx: ReturnType<typeof getInfluxQueryApi>,
    indicator: string,
    measurement: string | string[],
    qOpts: IndicatorQueryOptions,
    nutsArray: string[],
    chunkSize = 100
): Promise<InfluxDataPoint[]> {
    const chunks = chunkArray(nutsArray, chunkSize);

    async function processChunk(subset: string[]): Promise<InfluxDataPoint[]> {
        try {
            const partialOpts: IndicatorQueryOptions = {
                ...qOpts,
                filters: { ...qOpts.filters, nuts_id: subset }
            };

            const partialQuery = generateIndicatorQuery(indicator, measurement, partialOpts);
            const partialResults: InfluxDataPoint[] = await influx.collectRows(partialQuery);
            return partialResults.map(({ result, table, ...rest }) => rest);
        } catch (error) {
            console.error('Error processing chunk:', error);
            return [];
        }
    }

    let results: InfluxDataPoint[][] = [];

    switch (true) {
        case qOpts.limit !== undefined:
            results = await Promise.all(chunks.map(processChunk));
            break;
        default:
            const concurrencyLimit = 20;
            for (let i = 0; i < chunks.length; i += concurrencyLimit) {
                const batch = chunks.slice(i, i + concurrencyLimit);
                const batchResults = await Promise.all(batch.map(processChunk));
                results.push(...batchResults);
            }
            break;
    }

    return results.flat();
}

export async function fetchChunkedPriceData(
    influx: ReturnType<typeof getInfluxQueryApi>,
    qOpts: PriceQueryOptions,
    nutsArray: string[],
    chunkSize = 100
): Promise<InfluxDataPoint[]> {
    const chunks = chunkArray(nutsArray, chunkSize);

    async function processChunk(subset: string[]): Promise<InfluxDataPoint[]> {
        try {
            const partialOpts: PriceQueryOptions = {
                ...qOpts,
                filters: { ...qOpts.filters, nuts_id: subset }
            };

            const partialQuery = generatePriceQuery('price', partialOpts);
            const partialResults: InfluxDataPoint[] = await influx.collectRows(partialQuery);
            return partialResults.map(({ result, table, ...rest }) => rest);
        } catch (error) {
            console.error('Error processing chunk:', error);
            return [];
        }
    }

    let results: InfluxDataPoint[][] = [];

    switch (true) {
        case qOpts.limit !== undefined:
            results = await Promise.all(chunks.map(processChunk));
            break;
        default:
            const concurrencyLimit = 20;
            for (let i = 0; i < chunks.length; i += concurrencyLimit) {
                const batch = chunks.slice(i, i + concurrencyLimit);
                const batchResults = await Promise.all(batch.map(processChunk));
                results.push(...batchResults);
            }
            break;
    }

    return results.flat();
}

// Response Building Functions
export function buildSingleResponse(
    dataPoints: any[],
    mode: Mode,
    nutsId: string,
    indicator: any,
    includeProduction: boolean,
    endpointType: 'impacts' | 'power_breakdown' = 'impacts'
): ResponseData | null {
    if (!dataPoints || dataPoints.length === 0) {
        return null;
    }

    const byTime = groupByKey(dataPoints, 'dt');
    const transformedData = Object.entries(byTime).map(([dt, points]) => {
        if (endpointType === 'impacts') {
            return {
                dt: parseAndFormatDate(dt),
                consumption: points[0]?.consumption,
                production: includeProduction ? points[0]?.production : undefined
            };
        }
        // For power breakdown data, keep the existing structure
        return {
            dt: parseAndFormatDate(dt),
            power_breakdown: transformResourceGroups(points as any[], includeProduction)
        };
    });

    return {
        id: indicator.id_short,
        indicator: indicator.indicator_extended,
        units: indicator.units,
        mode,
        nuts_id: nutsId,
        data: transformedData
    };
}

export function buildDataDictionary(
    groupedData: Record<string, any[]>,
    codes: string[],
    includeProduction: boolean,
    endpointType: 'impacts' | 'power_breakdown' = 'impacts'
): Record<string, DataPoint[]> {
    const finalDict: Record<string, DataPoint[]> = {};

    for (const code of codes) {
        if (!groupedData[code] || groupedData[code].length === 0) {
            finalDict[code] = [];
            continue;
        }

        const byTime = groupByKey(groupedData[code], 'dt');
        finalDict[code] = Object.entries(byTime).map(([dt, points]) => {
            if (endpointType === 'impacts') {
                return {
                    dt: parseAndFormatDate(dt),
                    consumption: points[0]?.consumption,
                    production: includeProduction ? points[0]?.production : undefined
                };
            }
            return {
                dt: parseAndFormatDate(dt),
                power_breakdown: transformResourceGroups(points, includeProduction)
            };
        });
    }

    return finalDict;
}


export function validateSubscription(req: AuthRequest, nutsArray: string[]): ValidationError | null {
    if (!req.subscription) {
        return {
            status: 401,
            body: {
                message: 'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
            }
        };
    }

    if (!req.isGod) {
        if (!req.subscription.locations) {
            return {
                status: 400,
                body: {
                    message: 'No zones for your subscription.'
                }
            };
        }
        const missingInSubscription = nutsArray.filter(
            (code) => !req.subscription?.locations?.includes(code)
        );

        if (missingInSubscription.length > 0) {
            return {
                status: 404,
                body: {
                    message: `No data found for these codes, as they're not in your subscription: [${missingInSubscription.join(', ')}]`
                }
            };
        }
    }

    return null;
}

export function validateHistoricalAccess(req: AuthRequest): ValidationError | null {
    if (
        req.subscription?.plan_name === 'basic' &&
        req.subscription?.subscription_period === 'monthly'
    ) {
        return {
            status: 403,
            body: {
                message: 'Historical data access is not available with Basic (monthly) subscription. Please upgrade your plan to Basic (yearly) subscription to access historical data.'
            }
        };
    }
    return null;
}