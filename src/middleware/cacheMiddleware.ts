import { Request, Response, NextFunction } from 'express';
import NodeCache from 'node-cache';

const cache = new NodeCache({ stdTTL: 1 * 60 * 60 }); // 1 hr

export const cacheMiddleware = (duration: number) => {
    return (req: Request, res: Response, next: NextFunction) => {
        if (req.method !== 'GET') {
            return next();
        }

        const isSwagger = req.headers['x-swagger-source'] === 'swagger-ui';
        const cacheKey = `${req.originalUrl}-${isSwagger ? 'swagger' : 'non-swagger'}`;

        const cachedResponse = cache.get(cacheKey);

        if (cachedResponse) {
            (req as any).cachedResponse = cachedResponse;
        }

        const originalJson = res.json.bind(res);

        res.json = function (body: any): Response {
            if (!(req as any).cachedResponse) {
                cache.set(cacheKey, body, duration);
            }
            return originalJson(body);
        } as any;

        next();
    };
};

export const serveCachedResponse = (req: Request, res: Response, next: NextFunction) => {
    if ((req as any).cachedResponse) {
        return res.json((req as any).cachedResponse);
    }

    next();
};
