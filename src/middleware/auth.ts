import { Request, Response, NextFunction } from 'express';
import { getSupabaseAdminClient } from '../supabaseClient';
import { Database } from '../types/SupabaseDefinition';
import { getSubPlan } from '../utils/cachedGetters';
import { RateLimitInfo } from 'express-rate-limit';

type Subscription = Database['public']['Functions']['fetch_active_subscription_data']['Returns'];

export interface AuthRequest extends Request {
    subscription?: Subscription | null;
    indicators?: string[];
    plan?: Database['public']['Tables']['subscription_plans']['Row'];
    rateLimit?: RateLimitInfo;
    isGod?: boolean;
}

export function authenticateToken() {
    return async (req: AuthRequest, res: Response, next: NextFunction) => {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (authHeader && authHeader.split(' ')[0] !== 'Bearer') {
            return res.status(401).json({
                status: 'error',
                code: 401,
                message:
                    'Wrong format of Authorization header. Ensure a valid user token is included in the request headers.'
            });
        }

        if (token == null)
            return res.status(401).json({
                status: 'error',
                code: 401,
                message:
                    'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
            });
        req.isGod = false;
        const godToken = process.env.GOD_TOKEN;

        if (token === godToken) {
            req.subscription = {
                subscription_id: 9999999,
                details_id: 9999999,
                plan_name: 'god_plan',
                user_id: 1,
                subscription_period: 'infinite',
                locations: ['all']
            } as Subscription;

            req.plan = {
                plan_name: 'god_plan',
                display_name: 'God Mode',
                product_id: 'prod_god',
                created_at: new Date().toISOString(),
                tag: 'Best value',
                base_RL: 999999999,
                RL_per_location: 999999999,
                description: 'Unlimited access to all locations and data.',
                features: [
                    'Unlimited Locations',
                    'All Indicators',
                    'Real-time & Historical Data',
                    'Unlimited API calls/month',
                    'No extra cost for additional locations'
                ]
            };
            req.isGod = true;

            return next();
        }

        if (token.length !== 32 || !token.startsWith('em_')) {
            return res.status(403).send('Invalid token format');
        }

        const sb = getSupabaseAdminClient();
        const { data: subscription, error } = await sb.rpc('fetch_active_subscription_data', {
            api_token: token
        });

        if (error || !subscription || !subscription.subscription_id) {
            return res.sendStatus(401);
        }
        req.subscription = subscription;
        // req.indicators = await getSubIndicators(subscription.plan_name); //this is cached ;)
        req.plan = await getSubPlan(subscription.plan_name); //this is cached ;)
        next();
    };
}
