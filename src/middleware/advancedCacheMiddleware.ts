import { Request, Response, NextFunction } from 'express';
import { getCache, setCache } from '../lib/redis-utils';
import crypto from 'crypto';

// Generate a cache key based on the request path, query parameters, and body
const generateCacheKey = (req: Request): string => {
  const path = req.originalUrl;
  const query = JSON.stringify(req.query);
  const body = JSON.stringify(req.body);
  const authHeader = req.headers.authorization ? req.headers.authorization.substring(0, 20) : '';
  
  // Create a hash of the combined data to use as the cache key
  const dataToHash = `${path}:${query}:${body}:${authHeader}`;
  return `cache:${crypto.createHash('md5').update(dataToHash).digest('hex')}`;
};

// Calculate a hash of the response data to detect changes
const calculateDataHash = (data: any): string => {
  const stringifiedData = JSON.stringify(data);
  return crypto.createHash('md5').update(stringifiedData).digest('hex');
};

// Middleware to cache responses and only update when data changes
export const advancedCacheMiddleware = (defaultTTL: number = 3600) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    const cacheKey = generateCacheKey(req);
    
    try {
      // Try to get the cached response
      const cachedData = await getCache(cacheKey);
      
      if (cachedData) {
        const parsedCache = JSON.parse(cachedData);
        
        // Return the cached response
        return res.status(200).json(parsedCache.data);
      }
      
      // If no cache exists, modify the response object to capture the response data
      const originalJson = res.json;
      res.json = function(data: any) {
        // Calculate hash of the new data
        const dataHash = calculateDataHash(data);
        
        // Store the response data and its hash in the cache
        const cacheData = {
          data,
          hash: dataHash,
          timestamp: Date.now()
        };
        
        setCache(cacheKey, JSON.stringify(cacheData), defaultTTL)
          .catch(err => console.error('Error caching response:', err));
        
        // Call the original json method
        return originalJson.call(this, data);
      };
      
      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      next();
    }
  };
};

// Middleware to update cache when data changes
export const updateCacheOnChange = (defaultTTL: number = 3600) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Only apply to POST, PUT, DELETE requests
    if (!['POST', 'PUT', 'DELETE'].includes(req.method)) {
      return next();
    }
    
    // Store the original end method
    const originalEnd = res.end;
    const originalJson = res.json;
    
    // Override the json method to capture the response
    res.json = function(data: any) {
      try {
        // Generate a pattern-based cache key for the affected resources
        const resourcePattern = req.baseUrl.split('/').filter(Boolean)[0] || 'all';
        const patternKey = `cache:pattern:${resourcePattern}`;
        
        // Invalidate all caches matching this pattern
        // This is a simplified approach - in a real implementation, you might want to
        // be more selective about which caches to invalidate
        setCache(patternKey, JSON.stringify({ invalidated: true }), 60)
          .catch(err => console.error('Error invalidating cache:', err));
      } catch (error) {
        console.error('Error in updateCacheOnChange middleware:', error);
      }
      
      // Call the original json method
      return originalJson.call(this, data);
    };
    
    // Also override the end method to ensure we catch all responses
    res.end = function(chunk?: any, encoding?: any, cb?: any) {
      try {
        // Similar cache invalidation logic as above
        const resourcePattern = req.baseUrl.split('/').filter(Boolean)[0] || 'all';
        const patternKey = `cache:pattern:${resourcePattern}`;
        
        setCache(patternKey, JSON.stringify({ invalidated: true }), 60)
          .catch(err => console.error('Error invalidating cache:', err));
      } catch (error) {
        console.error('Error in updateCacheOnChange middleware:', error);
      }
      
      // Call the original end method
      return originalEnd.call(this, chunk, encoding, cb);
    };
    
    next();
  };
}; 