import rateLimit, { RateLimitRequestHandler, Options } from 'express-rate-limit';
import { AuthRequest } from './auth';
import dotenv from 'dotenv';

dotenv.config();

const isTestEnvironment = process.env.NODE_ENV === 'test';

const baseOptions: Partial<Options> = {
    windowMs: 60 * 1000, // 1 min
    standardHeaders: true,
    legacyHeaders: false,
    message: `You've exceeded the limit for your subscription, please try again in a minute.`
};

export const createUserBasedRateLimiter = (): RateLimitRequestHandler => {
    const maxLimit = 10000;

    return rateLimit({
        ...baseOptions,
        max: (req: AuthRequest) => {
            const baseRL = req.plan?.base_RL ?? 10;
            const rlPerLocation = req.plan?.RL_per_location ?? 0;
            const numLocations = (req?.subscription?.locations?.length ?? 0) - 1;

            let totalRL: number;

            if (baseRL === -1) {
                totalRL = maxLimit;
            } 
            else if(req.isGod) {
                totalRL = maxLimit;
            }
            else {
                totalRL = baseRL + rlPerLocation * numLocations;
            }

            return isTestEnvironment ? Infinity : totalRL;
        }
    });
};

export const createHighBasedRateLimiter = (): RateLimitRequestHandler => {
    return rateLimit({
        ...baseOptions,
        max: (req: AuthRequest) => {
            const planName = req.plan?.plan_name;
            let totalRL: number;

            switch (planName) {
                case 'basic':
                    totalRL = 1; // TBD
                    break;
                case 'god_plan':
                    totalRL = 600;
                default:
                    totalRL = 1;
            }

            return isTestEnvironment ? Infinity : totalRL;
        }
    });
};