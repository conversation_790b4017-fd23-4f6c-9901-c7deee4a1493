import { Router, Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import { getThalesClient } from '../supabaseClient';
import { cacheMiddleware } from '../middleware/cacheMiddleware';

const router = Router();

router.use(cacheMiddleware(1 * 60 * 60)); // 1 hr

router.get('/', async (req: AuthRequest, res: Response) => {
    if (!req.subscription) {
        return res.status(401).json({
            message:
                'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
        });
    }

    const statusParam = req.query.status;
    let status = 'active';

    if (statusParam !== undefined) {
        status = statusParam.toString().toLowerCase();
        if (status !== 'active' && status !== 'inactive') {
            return res.status(400).json({
                message: 'Invalid status parameter. Valid values are "active" or "inactive".'
            });
        }
    }

    const is_active = status === 'active';

    const thales = getThalesClient();

    let data, error;

    if (req.headers['x-swagger-source'] === 'swagger-ui') {
        ({ data, error } = await thales
            .from('nuts_info')
            .select('nuts_id , nuts_geometries(geometry)')
            .eq('nuts', '0')
            .eq('is_active', is_active)
            .in('nuts_id', ['DE', 'CH', 'AT']));
    } else {
        ({ data, error } = await thales
            .from('nuts_info')
            .select('nuts_id , nuts_geometries(geometry)')
            .eq('nuts', '0')
            .eq('is_active', is_active));
    }

    if (error) {
        console.error('Database error:', error.message);
        return res.status(500).json({
            message:
                'An internal server error occurred. Please try again later or contact support if the issue persists.'
        });
    }

    if (!data || data.length === 0) {
        return res.status(404).json({
            message: 'No geometries found for the specified status.'
        });
    }

    res.status(200).json(data);
});

router.get('/:nuts_id', async (req: AuthRequest, res: Response) => {
    if (!req.subscription) {
        return res.status(401).json({
            message:
                'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
        });
    }

    const nutsIdParam = req.params.nuts_id;
    const nutsId = nutsIdParam.toUpperCase();

    if (!nutsIdParam || typeof nutsIdParam !== 'string') {
        return res.status(400).json({
            message:
                'The `nuts_id` path parameter is missing or invalid. Verify that the `nuts_id` is correct.'
        });
    }

    if (!req.subscription?.locations?.includes(nutsId) && nutsId !== 'all' && !req.isGod) {
        return res.status(404).json({
            message:
                'No data was found for the specified `nuts_id` since it is not included in your subscription.'
        });
    }

    if (!/^[A-Z0-9]{2,6}$/.test(nutsId)) {
        return res.status(400).json({
            message:
                'The `nuts_id` path parameter is invalid. Verify that the `nuts_id` is correct.'
        });
    }

    const thales = getThalesClient();
    const { data, error } = await thales
        .from('nuts_geometries')
        .select('*')
        .eq('nuts_id', nutsId)
        .limit(1)
        .maybeSingle();

    if (error) {
        console.error('Database error:', error.message);
        return res.status(500).json({
            message:
                'An internal server error occurred. Please try again later or contact support if the issue persists.'
        });
    }

    if (!data) {
        return res.status(404).json({
            message:
                'The specified nuts_id does not match any existing zone. Verify the ID and try again.'
        });
    }

    res.status(200).json(data);
});

export default router;
