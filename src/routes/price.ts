import { Router, Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import { getSupabaseAdminClient } from '../supabaseClient';
import { fetchChunkedPriceData, validateDate, validateHistoricalAccess, validateSubscription, parseAndFormatDate } from '../utils/utils';
import { getInfluxQueryApi } from '../influxdb/influxClient';
import { generatePriceQuery, generateExcludeInactivePriceQuery, PriceQueryOptions } from '../influxdb/queries';
import { InfluxDataPoint } from '../utils/dataTransforms';
import { DataPoint } from '../utils/types';

const router = Router();

/**
 * @route GET /price/:mode/:nuts_id
 * @description Retrieves electricity price data for specified NUTS regions.
 * 
 * Supports three modes:
 * - 'last': Returns only the most recent price data point for each NUTS code
 * - 'last24h': Returns price data for the last 24 hours (up to 96 data points)
 * - 'historical': Returns price data for a specified date range
 * 
 * @param {Object} req.params - URL parameters
 * @param {string} req.params.mode - Data retrieval mode ('last', 'last24h', 'historical')
 * @param {string} req.params.nuts_id - NUTS region ID(s) - can be a single code, comma-separated list, or 'all'
 * 
 * @param {Object} req.query - Query parameters
 * @param {string} [req.query.currency=EUR] - Currency for price data ('EUR' or 'GBP')
 * @param {string} [req.query.start_date] - Start date for historical data (required for 'historical' mode)
 * @param {string} [req.query.end_date] - End date for historical data (defaults to start_date + 35 days)
 * 
 * @returns {Object} Response containing:
 *   - id: 'price'
 *   - indicator: 'Electricity Price'
 *   - units: Currency per MWh
 *   - mode: The requested mode
 *   - nuts_id: The requested NUTS ID(s)
 *   - data: Price data points with timestamps
 * 
 * @throws {400} - Invalid request parameters
 * @throws {401} - Unauthorized access (missing subscription)
 * @throws {403} - Subscription doesn't include requested NUTS region or historical access
 * @throws {404} - No price data available for the specified parameters
 * @throws {500} - Server error during data retrieval
 */
router.get('/:mode/:nuts_id', async (req: AuthRequest, res: Response) => {
    try {
        const { mode, nuts_id } = req.params;
        const { start_date, end_date, currency: requestedCurrency = 'EUR' } = req.query;
        const supabase = getSupabaseAdminClient();

        // Validate mode
        if (!['last', 'last24h', 'historical'].includes(mode)) {
            return res.status(400).json({
                message: 'Invalid mode parameter. Valid values are "last", "last24h", "historical".'
            });
        }

        // Determine currency based on nuts_id
        const currency = nuts_id.toUpperCase().startsWith('UK') ? 'GBP' : requestedCurrency;

        // Validate currency
        if (!['EUR', 'GBP'].includes(currency as string)) {
            return res.status(400).json({
                message: 'Invalid currency. Valid values are "EUR" or "GBP".'
            });
        }

        // Get NUTS codes
        let nutsArray: string[] = [];

        if (nuts_id.toLowerCase() === 'all') {
            const { data, error } = await supabase
                .schema('thales')
                .from('nuts_info')
                .select('nuts_id')
                .eq('is_active', true);

            if (error) {
                console.error('Supabase error:', error);
                return res.status(500).json({
                    message: 'Error fetching active nuts from database. Please try again later or contact support.'
                });
            }
            if (!data || data.length === 0) {
                return res.status(404).json({
                    message: 'No active NUTS codes found.'
                });
            }
            nutsArray = data.map((row) => row.nuts_id.toUpperCase());
        } else if (nuts_id.includes(',')) {
            nutsArray = nuts_id
                .split(',')
                .map((code) => code.trim().toUpperCase())
                .filter((code) => code.length > 0);
        } else {
            nutsArray = [nuts_id.toUpperCase()];
        }

        // Validate subscription
        const subscriptionError = validateSubscription(req, nutsArray);
        if (subscriptionError) {
            return res.status(subscriptionError.status).json(subscriptionError.body);
        }

        // Validate historical access
        if (mode === 'historical') {
            const historicalError = validateHistoricalAccess(req);
            if (historicalError) {
                return res.status(historicalError.status).json(historicalError.body);
            }
        }

        // Prepare query options
        const influx = getInfluxQueryApi();
        const qOpts: PriceQueryOptions = {
            filters: { 
                nuts_id: nutsArray,
                currency: currency as string
            },
            renameTo: 'price',
            limit: req.headers['x-swagger-source'] === 'swagger-ui' 
                ? 5 
                : mode === 'last'
                    ? 1 
                    : mode === 'last24h'
                        ? 96 
                        : nuts_id === 'all' ? 480 : undefined,
            getLast: mode === 'last' ? true : ['last24h', 'historical'].includes(mode) ? false : undefined,
        };

        // Handle different modes
        if (mode === 'last' || mode === 'last24h') {
            // Fetch data
            let finalResults: InfluxDataPoint[];
            if (nutsArray.length > 10) {
                finalResults = await fetchChunkedPriceData(
                    influx,
                    qOpts,
                    nutsArray,
                    50
                );
            } else {
                const query = generatePriceQuery('price', qOpts);
                finalResults = await influx.collectRows(query);
                finalResults = finalResults.map(({ result, table, ...rest }) => rest);
            }

            // Process results
            const timeSeriesData: Record<string, DataPoint[]> = {};
            
            // Process data points
            for (const result of finalResults) {
                const code = result.nuts_id;
                const value = result.price;
                const dt = result.dt;
                
                if (value !== null && value !== undefined && !isNaN(value) && dt) {
                    if (!timeSeriesData[code]) {
                        timeSeriesData[code] = [];
                    }
                    timeSeriesData[code].push({
                        dt: parseAndFormatDate(dt),
                        value: Number(value.toFixed(2))
                    });
                }
            }

            // Return response
            if (nutsArray.length === 1) {
                const singleCode = nutsArray[0];
                const data = timeSeriesData[singleCode];

                if (!data || data.length === 0) {
                    return res.status(404).json({
                        message: 'No price data available for the specified parameters.'
                    });
                }

                return res.status(200).json({
                    id: 'price',
                    indicator: 'Electricity Price',
                    units: `${currency}/MWh`,
                    mode,
                    nuts_id: singleCode,
                    data: data
                });
            }

            return res.status(200).json({
                id: 'price',
                indicator: 'Electricity Price',
                units: `${currency}/MWh`,
                mode,
                nuts_id: nutsArray,
                data: timeSeriesData
            });
        }

        // Historical mode
        else if (mode === 'historical') {
            if (!start_date) {
                return res.status(400).json({
                    message: 'Start date for historical data is required (start_date).'
                });
            }

            const startDateParam = Array.isArray(start_date) ? start_date[0] : start_date;
            const startDate = validateDate(startDateParam as string);
            if (!startDate) {
                return res.status(400).json({
                    message: 'Invalid start_date. Must be a valid date in ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).'
                });
            }

            const endDateParam = Array.isArray(end_date) ? end_date[0] : end_date;
            let endDate: Date;
            if (endDateParam === undefined) {
                endDate = new Date(startDate.getTime() + 35 * 24 * 60 * 60 * 1000 - 1);
            } else {
                const validatedEndDate = validateDate(endDateParam as string);
                if (!validatedEndDate) {
                    return res.status(400).json({
                        message: 'Invalid end_date. Must be a valid date in ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).'
                    });
                }
                endDate = validatedEndDate;
            }

            const now = new Date();
            if (endDate.getTime() > now.getTime()) {
                endDate = now;
            }

            qOpts.start = startDate.toISOString();
            qOpts.end = endDate.toISOString();

            // Fetch data
            let finalResults: InfluxDataPoint[] = [];
            if (nutsArray.length > 10) {
                finalResults = await fetchChunkedPriceData(
                    influx,
                    qOpts,
                    nutsArray,
                    50
                );
            } else {
                const query = generatePriceQuery('price', qOpts);
                finalResults = await influx.collectRows(query);
                finalResults = finalResults.map(({ result, table, ...rest }) => rest);
            }

            // Process results
            const timeSeriesData: Record<string, DataPoint[]> = {};
            
            // Process data points
            for (const result of finalResults) {
                const code = result.nuts_id;
                const value = result.price;
                const dt = result.dt;
                
                if (value !== null && value !== undefined && !isNaN(value) && dt) {
                    if (!timeSeriesData[code]) {
                        timeSeriesData[code] = [];
                    }
                    timeSeriesData[code].push({
                        dt: parseAndFormatDate(dt),
                        value: Number(value.toFixed(2))
                    });
                }
            }

            // Return response
            if (nutsArray.length === 1) {
                const singleCode = nutsArray[0];
                const data = timeSeriesData[singleCode];

                if (!data || data.length === 0) {
                    return res.status(404).json({
                        message: 'No price data available for the specified parameters.'
                    });
                }

                return res.status(200).json({
                    id: 'price',
                    indicator: 'Electricity Price',
                    units: `${currency}/MWh`,
                    mode,
                    nuts_id: singleCode,
                    data: data
                });
            }

            return res.status(200).json({
                id: 'price',
                indicator: 'Electricity Price',
                units: `${currency}/MWh`,
                mode,
                nuts_id: nutsArray,
                data: timeSeriesData
            });
        }
    } catch (error) {
        console.error('Internal server error:', error);
        return res.status(500).json({
            message: 'An internal server error occurred. Please try again later or contact support if the issue persists.'
        });
    }
});

/**
 * @route GET /price/exclude-inactive
 * @description Retrieves electricity price data for the last 24 hours for all inactive NUTS regions.
 * 
 * @param {Object} req.query - Query parameters
 * @param {string} [req.query.currency=EUR] - Currency for price data ('EUR' or 'GBP')
 * 
 * @returns {Object} Response containing:
 *   - id: 'price'
 *   - indicator: 'Electricity Price'
 *   - units: Currency per MWh
 *   - nuts_id: List of inactive NUTS regions
 *   - data: Price data points with timestamps for the last 24 hours
 * 
 * @throws {401} - Unauthorized access (missing subscription)
 * @throws {403} - Subscription doesn't include requested NUTS region
 * @throws {404} - No inactive NUTS regions found
 * @throws {500} - Server error during data retrieval
 */
router.get('/exclude-inactive', async (req: AuthRequest, res: Response) => {
    try {
        const { currency: requestedCurrency = 'EUR' } = req.query;
        const supabase = getSupabaseAdminClient();

        // Validate currency
        if (!['EUR', 'GBP'].includes(requestedCurrency as string)) {
            return res.status(400).json({
                message: 'Invalid currency. Valid values are "EUR" or "GBP".'
            });
        }

        // Get inactive NUTS codes
        const { data: inactiveNuts, error: inactiveError } = await supabase
            .schema('thales')
            .from('nuts_info')
            .select('nuts_id')
            .eq('is_active', false);

        if (inactiveError) {
            console.error('Supabase error:', inactiveError);
            return res.status(500).json({
                message: 'Error fetching inactive nuts from database. Please try again later or contact support.'
            });
        }

        if (!inactiveNuts || inactiveNuts.length === 0) {
            return res.status(404).json({
                message: 'No inactive NUTS regions found.'
            });
        }

        const inactiveNutsIds = inactiveNuts.map(row => row.nuts_id.toUpperCase());

        // Get active NUTS codes for response
        const { data: activeNuts, error: activeError } = await supabase
            .schema('thales')
            .from('nuts_info')
            .select('nuts_id')
            .eq('is_active', true);

        if (activeError) {
            console.error('Supabase error:', activeError);
            return res.status(500).json({
                message: 'Error fetching active nuts from database. Please try again later or contact support.'
            });
        }

        const activeNutsIds = activeNuts.map(row => row.nuts_id.toUpperCase());

        // Filter active NUTS IDs based on currency
        const filteredActiveNutsIds = requestedCurrency === 'GBP' 
            ? activeNutsIds.filter(id => id.startsWith('UK'))
            : activeNutsIds.filter(id => !id.startsWith('UK'));

        // Validate subscription
        const subscriptionError = validateSubscription(req, inactiveNutsIds);
        if (subscriptionError) {
            return res.status(subscriptionError.status).json(subscriptionError.body);
        }

        // Prepare query options
        const influx = getInfluxQueryApi();
        const qOpts: PriceQueryOptions = {
            filters: { 
                nuts_id: inactiveNutsIds,
                currency: requestedCurrency as string
            },
            renameTo: 'price',
            limit: 96, // Last 24h of data
            getLast: false,
        };

        const query = generateExcludeInactivePriceQuery('price', qOpts);
        let finalResults = await influx.collectRows(query);
        finalResults = finalResults.map((row: any) => {
            const { result, table, ...rest } = row;
            return rest;
        }) as InfluxDataPoint[];

        // Process results
        const timeSeriesData: Record<string, DataPoint[]> = {};
        
        // Process data points
        for (const result of finalResults) {
            const code = (result as InfluxDataPoint).nuts_id;
            const value = (result as InfluxDataPoint).price;
            const dt = (result as InfluxDataPoint).dt;
            
            if (value !== null && value !== undefined && !isNaN(value) && dt) {
                if (!timeSeriesData[code]) {
                    timeSeriesData[code] = [];
                }
                timeSeriesData[code].push({
                    dt: parseAndFormatDate(dt),
                    value: Number(value.toFixed(2))
                });
            }
        }

        // Return response
        return res.status(200).json({
            id: 'price',
            indicator: 'Electricity Price',
            units: `${requestedCurrency}/MWh`,
            nuts_id: filteredActiveNutsIds,
            data: timeSeriesData
        });

    } catch (error) {
        console.error('Internal server error:', error);
        res.status(500).json({
            message: 'An internal server error occurred. Please try again later or contact support if the issue persists.'
        });
    }
});

export default router;
