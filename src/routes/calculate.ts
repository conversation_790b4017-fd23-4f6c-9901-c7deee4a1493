import { Router, Response } from "express";
import { getInfluxQueryApi } from "../influxdb/influxClient";
import { getThalesIndicators } from "../utils/cachedGetters";
import { generateIndicatorQuery } from "../influxdb/queries";

const router = Router();

/**
 * @route POST /calculate
 * @description Calculates emissions based on a provided consumption profile and emission factors.
 * 
 * @param {Object} req.body - Request body containing consumption profile and parameters
 * @param {Array} req.body.consumption_profile - Array of objects with timestamp and consumption value in kWh
 * @param {string} req.body.frequency - Data frequency ('15m', '30m', '1h', '1d')
 * @param {string} req.body.nuts_id - NUTS region ID for which to calculate emissions
 * @param {string} req.body.units - Must be 'kWh'
 * @param {string} [req.body.mode] - Emission factor indicator to use (defaults to 'ipcc_2021_climate_change_gwp100a')
 * 
 * @returns {Object} Response containing:
 *   - indicator: Name of the emission factor used
 *   - units: 'kgCO2-eq'
 *   - frequency: Same as input frequency
 *   - total: Total cumulative emissions in kgCO2-eq
 *   - profile: Array of timestamped emission values
 * 
 * @throws {400} - Invalid request parameters or no emission factors found
 * @throws {401} - Unauthorized access (missing subscription)
 * @throws {403} - Subscription doesn't include requested NUTS region
 * @throws {500} - Server error during calculation
 */
router.post('/', async (req: any, res: Response) => {
    if (!req.subscription) {
        return res.status(401).json({
            message: 'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
        });
    }

    try {
        const { consumption_profile, frequency, mode, nuts_id, units } = req.body;

        // Validate request body
        if (!consumption_profile || !Array.isArray(consumption_profile) || consumption_profile.length === 0) {
            return res.status(400).json({
                error: 'Invalid consumption profile. Expected an array of timestamped consumption data.'
            });
        }

        if (!frequency) {
            return res.status(400).json({
                error: 'Frequency is required (e.g., "15min", "30min", "1h", "1d").'
            });
        }

        const validFrequencies = ['15min', '30min', '1h', '1d'];
        if (!validFrequencies.includes(frequency)) {
            return res.status(400).json({
                error: `Invalid frequency. Must be one of: ${validFrequencies.join(', ')}.`
            });
        }

        const frequencyMs = (() => {
            switch (frequency) {
                case '15min': return 15 * 60 * 1000;
                case '30min': return 30 * 60 * 1000;
                case '1h': return 60 * 60 * 1000;
                case '1d': return 24 * 60 * 60 * 1000;
                default: throw new Error('Invalid frequency');
            }
        })();

        if (!nuts_id) {
            return res.status(400).json({
                error: 'NUTS ID is required (e.g., "DE", "FR", etc.).'
            });
        }

        if (!units || units.toLowerCase() !== 'kwh') {
            return res.status(400).json({
                error: 'Units must be specified as "kWh" in the request body.'
            });
        }

        // Validate subscription
        if (!req.isGod && (!req.subscription.locations || !req.subscription.locations.includes(nuts_id))) {
            return res.status(403).json({
                error: `Access denied. Your subscription does not include data for NUTS ID: ${nuts_id}`
            });
        }

        // Validate consumption profile
        for (const item of consumption_profile) {
            if (!item.timestamp || !item.value || typeof item.value !== 'number') {
                return res.status(400).json({
                    error: 'Each item in consumption profile must have a timestamp and numeric value.'
                });
            }

            const timestamp = new Date(item.timestamp);
            if (isNaN(timestamp.getTime())) {
                return res.status(400).json({
                    error: `Invalid timestamp format: ${item.timestamp}. Timestamps must be in ISO 8601 format (e.g., "2024-03-20T10:00:00Z").`
                });
            }

            if (item.value < 0 || item.value > 100000) {
                return res.status(400).json({
                    error: `Invalid consumption value: ${item.value}. Values must be positive and within reasonable kWh range (0 to 100,000 kWh).`
                });
            }
        }

        // Validate timestamps match frequency
        const timestamps = consumption_profile.map(item => new Date(item.timestamp));
        for (let i = 1; i < timestamps.length; i++) {
            const timeDiff = timestamps[i].getTime() - timestamps[i-1].getTime();
            if (timeDiff !== frequencyMs) {
                return res.status(400).json({
                    error: `Timestamps do not match the specified frequency of ${frequency}. Expected ${frequencyMs}ms between timestamps, but found ${timeDiff}ms between timestamps ${timestamps[i-1].toISOString()} and ${timestamps[i].toISOString()}.`
                });
            }
        }

        // Get indicator
        const indicator = mode || 'ipcc_2021_climate_change_gwp100a'; 
        const thalesIndicator = (await getThalesIndicators()).find((x) => x.id_short === indicator) || {
            id_short: 'gwp100a',
            indicator_extended: 'Global Warming Potential (100 years)',
            units: 'kgCO2-eq'
        };

        // Get emission factors
        const influx = getInfluxQueryApi();
        const queryOptions = {
            start: new Date(consumption_profile[0].timestamp).toISOString(),
            end: new Date(consumption_profile[consumption_profile.length - 1].timestamp).toISOString(),
            filters: { nuts_id: nuts_id.toUpperCase() },
            getLast: false,
            frequency: frequency
        };

        const query = generateIndicatorQuery(indicator, 'consumption', queryOptions);
        const influxData = await influx.collectRows(query);

        // Create emission factor map
        const emissionFactorMap = new Map(
            influxData.map((data: any) => {
                try {
                    if (!data.dt) {
                        console.error('Missing dt field in InfluxDB data point:', data);
                        return null;
                    }

                    let influxDate: Date;
                    if (typeof data.dt === 'string') {
                        influxDate = new Date(data.dt);
                    } else if (typeof data.dt === 'number') {
                        influxDate = new Date(data.dt);
                    } else {
                        console.error('Invalid dt format in InfluxDB data point:', data.dt);
                        return null;
                    }

                    if (isNaN(influxDate.getTime())) {
                        console.error(`Invalid timestamp from InfluxDB: ${data.dt}`);
                        return null;
                    }

                    // Normalize date based on frequency
                    let normalizedDate: Date;
                    switch (frequency) {
                        case '15min':
                            normalizedDate = new Date(Date.UTC(
                                influxDate.getUTCFullYear(),
                                influxDate.getUTCMonth(),
                                influxDate.getUTCDate(),
                                influxDate.getUTCHours(),
                                Math.floor(influxDate.getUTCMinutes() / 15) * 15
                            ));
                            break;
                        case '30min':
                            normalizedDate = new Date(Date.UTC(
                                influxDate.getUTCFullYear(),
                                influxDate.getUTCMonth(),
                                influxDate.getUTCDate(),
                                influxDate.getUTCHours(),
                                Math.floor(influxDate.getUTCMinutes() / 30) * 30
                            ));
                            break;
                        case '1h':
                            normalizedDate = new Date(Date.UTC(
                                influxDate.getUTCFullYear(),
                                influxDate.getUTCMonth(),
                                influxDate.getUTCDate(),
                                influxDate.getUTCHours()
                            ));
                            break;
                        case '1d':
                            normalizedDate = new Date(Date.UTC(
                                influxDate.getUTCFullYear(),
                                influxDate.getUTCMonth(),
                                influxDate.getUTCDate()
                            ));
                            break;
                        default:
                            normalizedDate = new Date(Date.UTC(
                                influxDate.getUTCFullYear(),
                                influxDate.getUTCMonth(),
                                influxDate.getUTCDate(),
                                influxDate.getUTCHours(),
                                influxDate.getUTCMinutes()
                            ));
                    }

                    return [normalizedDate.getTime(), data.value];
                } catch (error) {
                    console.error(`Error processing InfluxDB timestamp: ${data.dt}`, error);
                    return null;
                }
            }).filter((entry): entry is [number, number] => entry !== null)
        );

        // Calculate emissions
        const emissionsProfile = consumption_profile
            .map(item => {
                try {
                    const consumptionDate = new Date(item.timestamp);
                    if (isNaN(consumptionDate.getTime())) {
                        console.error(`Invalid consumption timestamp: ${item.timestamp}`);
                        return null;
                    }

                    // Normalize consumption date based on frequency
                    let normalizedDate: Date;
                    switch (frequency) {
                        case '15min':
                            normalizedDate = new Date(Date.UTC(
                                consumptionDate.getUTCFullYear(),
                                consumptionDate.getUTCMonth(),
                                consumptionDate.getUTCDate(),
                                consumptionDate.getUTCHours(),
                                Math.floor(consumptionDate.getUTCMinutes() / 15) * 15
                            ));
                            break;
                        case '30min':
                            normalizedDate = new Date(Date.UTC(
                                consumptionDate.getUTCFullYear(),
                                consumptionDate.getUTCMonth(),
                                consumptionDate.getUTCDate(),
                                consumptionDate.getUTCHours(),
                                Math.floor(consumptionDate.getUTCMinutes() / 30) * 30
                            ));
                            break;
                        case '1h':
                            normalizedDate = new Date(Date.UTC(
                                consumptionDate.getUTCFullYear(),
                                consumptionDate.getUTCMonth(),
                                consumptionDate.getUTCDate(),
                                consumptionDate.getUTCHours()
                            ));
                            break;
                        case '1d':
                            normalizedDate = new Date(Date.UTC(
                                consumptionDate.getUTCFullYear(),
                                consumptionDate.getUTCMonth(),
                                consumptionDate.getUTCDate()
                            ));
                            break;
                        default:
                            normalizedDate = new Date(Date.UTC(
                                consumptionDate.getUTCFullYear(),
                                consumptionDate.getUTCMonth(),
                                consumptionDate.getUTCDate(),
                                consumptionDate.getUTCHours(),
                                consumptionDate.getUTCMinutes()
                            ));
                    }

                    const emissionFactor = emissionFactorMap.get(normalizedDate.getTime());
                    
                    if (emissionFactor === undefined) {
                        console.error(`No emission factor found for timestamp ${item.timestamp} (normalized: ${normalizedDate.toISOString()})`);
                        return null;
                    }
                    
                    const emissionValue = Number(((item.value * emissionFactor) / 1000).toFixed());
                    
                    return {
                        timestamp: item.timestamp,
                        value: emissionValue
                    };
                } catch (error) {
                    console.error(`Error processing consumption timestamp: ${item.timestamp}`, error);
                    return null;
                }
            })
            .filter((item): item is { timestamp: string; value: number } => item !== null);

        if (emissionsProfile.length === 0) {
            return res.status(400).json({
                error: 'No emission factors found for any of the provided timestamps. Please check the date range and try again.'
            });
        }

        // Calculate cumulative emissions
        let cumulativeEmissions = 0;
        const finalProfile = emissionsProfile.map(item => {
            cumulativeEmissions += item.value;
            return {
                timestamp: item.timestamp,
                value: Number(cumulativeEmissions.toFixed(2))
            };
        });

        // Return results
        return res.status(200).json({
            indicator: thalesIndicator.indicator_extended,
            units: 'kgCO2-eq',
            frequency,
            total: Number(cumulativeEmissions.toFixed(2)),
            profile: emissionsProfile,
        });
    } catch (error) {
        console.error('Error calculating emissions:', error);
        return res.status(500).json({
            error: 'An error occurred while calculating emissions.'
        });
    }
});

export default router;