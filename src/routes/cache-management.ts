import express from 'express';
import { CacheManager } from '../lib/cache-manager';

const router = express.Router();

// Get cache statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await CacheManager.getStats();
    res.status(200).json(stats);
  } catch (error) {
    console.error('Error getting cache stats:', error);
    res.status(500).json({ error: 'Failed to get cache stats' });
  }
});

// Get all cache keys
router.get('/keys', async (req, res) => {
  try {
    const keys = await CacheManager.getAllKeys();
    res.status(200).json({ keys });
  } catch (error) {
    console.error('Error getting cache keys:', error);
    res.status(500).json({ error: 'Failed to get cache keys' });
  }
});

// Get cache value for a specific key
router.get('/keys/:key', async (req, res) => {
  try {
    const { key } = req.params;
    const value = await CacheManager.getCacheValue(key);
    
    if (value === null) {
      return res.status(404).json({ error: 'Key not found' });
    }
    
    res.status(200).json({ key, value });
  } catch (error) {
    console.error('Error getting cache value:', error);
    res.status(500).json({ error: 'Failed to get cache value' });
  }
});

// Invalidate cache for a specific resource
router.delete('/invalidate/:resource', async (req, res) => {
  try {
    const { resource } = req.params;
    const count = await CacheManager.invalidateResource(resource);
    res.status(200).json({ message: `Invalidated ${count} cache entries for resource: ${resource}` });
  } catch (error) {
    console.error('Error invalidating cache:', error);
    res.status(500).json({ error: 'Failed to invalidate cache' });
  }
});

// Invalidate all caches
router.delete('/invalidate', async (req, res) => {
  try {
    await CacheManager.invalidateAll();
    res.status(200).json({ message: 'All caches invalidated' });
  } catch (error) {
    console.error('Error invalidating all caches:', error);
    res.status(500).json({ error: 'Failed to invalidate all caches' });
  }
});

export default router; 