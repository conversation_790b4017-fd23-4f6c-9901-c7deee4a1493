import { Router, Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import { getThalesClient } from '../supabaseClient';

const router = Router();

type NutsLevel = '0' | '1' | '2' | '3';

router.get('/', async (req: AuthRequest, res: Response) => {
    console.log('Requesting nuts_id:', req.subscription);
    console.log('req.query:', req.query);

    if (!req.subscription) {
        return res.status(401).json({
            message:
                'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
        });
    }

    const statusParam = req.query.status;
    let levelParam = req.query.level?.toString();

    let status: string | undefined;
    if (statusParam !== undefined) {
        status = statusParam.toString().toLowerCase();
        if (status !== 'active' && status !== 'inactive') {
            return res.status(400).json({
                message: `Invalid status parameter. Valid values are 'active' or 'inactive'`
            });
        }
    }

    let nutsLevel = '0' as NutsLevel;
    if (levelParam !== undefined) {
        if (!/^[0-3]$/.test(levelParam.toString())) {
            return res.status(400).json({
                message: 'Invalid NUTS level specified. Valid levels are 0, 1, 2, 3'
            });
        }
        nutsLevel = levelParam.toString() as NutsLevel;
    }

    const thales = getThalesClient();
    let query;

    if (req.headers['x-swagger-source'] === 'swagger-ui') {
        query = thales
            .from('nuts_info')
            .select('nuts_id, name, is_active')
            .eq('nuts', nutsLevel)
            .limit(5);

        if (status) {
            query.eq('is_active', status === 'active');
        }
    } else {
        query = thales.from('nuts_info').select('nuts_id, name, is_active').eq('nuts', nutsLevel);

        if (status) {
            query.eq('is_active', status === 'active');
        }
    }

    const { data, error } = await query;

    if (error) {
        console.error('Database error:', error.message);
        return res.status(500).json({
            message:
                'An internal server error occurred. Please try again later or contact support if the issue persists.'
        });
    }

    if (!data || data.length === 0) {
        return res.status(404).json({
            message: 'No zones were found matching the specified parameters.'
        });
    }

    res.status(200).json(data);
});

router.get('/:nuts_id', async (req: AuthRequest, res: Response) => {
    if (!req.subscription) {
        return res.status(401).json({
            message:
                'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
        });
    }

    const nutsIdParam = req.params.nuts_id;
    if (!nutsIdParam || typeof nutsIdParam !== 'string') {
        return res.status(400).json({
            message:
                'The `nuts_id` parameter is missing or invalid. Ensure that a valid NUTS ID is provided in the request.'
        });
    }

    const nutsId = nutsIdParam.toUpperCase();
    if (!/^[A-Z0-9]{2,6}$/.test(nutsId)) {
        return res.status(400).json({
            message:
                'The `nuts_id` parameter is invalid. Ensure that a valid NUTS ID is provided in the request.'
        });
    }

    const detailedChildrenParam = req.query.detailed_children;
    let detailedChildren: boolean = false;
    if (detailedChildrenParam !== undefined) {
        if (detailedChildrenParam === 'true') {
            detailedChildren = true;
        } else if (detailedChildrenParam === 'false') {
            detailedChildren = false;
        } else {
            return res.status(400).json({
                message:
                    'Invalid value for `detailed_children`. Valid values are "true" or "false".'
            });
        }
    }

    const thales = getThalesClient();
    const rpcFunc = detailedChildren
        ? 'get_nuts_details_with_detailed_children'
        : 'get_nuts_details_with_children';

    const { data, error } = await thales.rpc(rpcFunc, { input_nuts_id: nutsId });

    if (!data) {
        return res.status(404).json({
            message: `No data was found for the nuts_id '${nutsId}'. Verify the nuts_id and try again.`
        });
    }

    if (error) {
        return res.status(500).json({
            message:
                'An internal server error occurred. Please try again later or contact support if the issue persists.'
        });
    }

    res.status(200).json(data);
});

export default router;
