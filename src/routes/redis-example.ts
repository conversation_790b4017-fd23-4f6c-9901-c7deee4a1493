import express from 'express';
import { setCache, getCache, deleteCache } from '../lib/redis-utils';

const router = express.Router();

// Example route to set a value in Redis
router.post('/set', async (req, res) => {
  try {
    const { key, value, expireSeconds } = req.body;
    
    if (!key || !value) {
      return res.status(400).json({ error: 'Key and value are required' });
    }
    
    await setCache(key, value, expireSeconds);
    res.status(200).json({ message: 'Value set successfully' });
  } catch (error) {
    console.error('Error setting cache:', error);
    res.status(500).json({ error: 'Failed to set cache' });
  }
});

// Example route to get a value from Redis
router.get('/get/:key', async (req, res) => {
  try {
    const { key } = req.params;
    const value = await getCache(key);
    
    if (value === null) {
      return res.status(404).json({ error: 'Key not found' });
    }
    
    res.status(200).json({ key, value });
  } catch (error) {
    console.error('Error getting cache:', error);
    res.status(500).json({ error: 'Failed to get cache' });
  }
});

// Example route to delete a value from Redis
router.delete('/delete/:key', async (req, res) => {
  try {
    const { key } = req.params;
    await deleteCache(key);
    res.status(200).json({ message: 'Key deleted successfully' });
  } catch (error) {
    console.error('Error deleting cache:', error);
    res.status(500).json({ error: 'Failed to delete cache' });
  }
});

export default router; 