import { Router, Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import { getInfluxQueryApi } from '../influxdb/influxClient';
import { getMeasurementFields, getThalesIndicators } from '../utils/cachedGetters';
import { generateExcludeInactiveIndicatorQuery, generateIndicatorQuery, type IndicatorQueryOptions } from '../influxdb/queries';
import { groupByKey, transformConsProdData, type InfluxDataPoint } from '../utils/dataTransforms';
import dataCache from '../cache/dataCache';
import { getSupabaseAdminClient } from '../supabaseClient';
import { Mode, DataPoint } from '../utils/types';
import { buildDataDictionary, buildSingleResponse, fetchChunkedData, validateDate, validateHistoricalAccess, validateSubscription, parseAndFormatDate } from '../utils/utils';

const router = Router();

/**
 *
 * @route GET /indicators
 * @middleware cacheMiddleware - Caches response for 1 hour
 * @middleware serveCachedResponse - Serves cached response if available
 * @description Retrieves a list of all available indicators with their metadata.
 * 
 * @param {AuthRequest} req - Express request object with authentication data
 * @param {Response} res - Express response object
 * 
 * @returns {Object[]} 200 - List of indicators with metadata and availability status
 * @returns {Object} 401 - Unauthorized error if no valid subscription
 * @returns {Object} 500 - Internal server error
 */
router.get(
    '/',
    async (req: AuthRequest, res: Response) => {
        if (!req.subscription) {
            return res.status(401).json({
                message: 'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
            });
        }

        try {
            const thalesIndicators = await getThalesIndicators();
            const influxIndicators = await getMeasurementFields('production');

            res.status(200).json(
                thalesIndicators.map((x) => ({
                    id: x.id_short,
                    indicator: x.indicator_extended,
                    metadata: {
                        methodology: x.methodology,
                        units: x.units,
                        category: x.category,
                        system_model: x.system_model,
                        model_type: x.model_type,
                        alternative_name: x.display_name_short
                    },
                    is_available: influxIndicators.includes(x.id)
                }))
            );
        } catch (error) {
            console.error('Internal server error:', error);
            res.status(500).json({
                message: 'An internal server error occurred. Please try again later or contact support if the issue persists.'
            });
        }
    }
);

/**
 * 
 * @route GET /indicators/:indicator/:mode/:nuts_id
 * @description Retrieves indicator data for specific indicator, mode, and NUTS code(s).
 * 
 * @param {AuthRequest} req - Express request object with authentication data
 * @param {Response} res - Express response object
 * @param {string} req.params.indicator - Indicator ID to retrieve
 * @param {string} req.params.mode - Data mode: "last", "last24h", or "historical"
 * @param {string} req.params.nuts_id - NUTS code(s) to retrieve data for (single code, comma-separated list, or "all")
 * @param {string} [req.query.start_date] - Start date for historical data (required for historical mode)
 * @param {string} [req.query.end_date] - End date for historical data (defaults to start_date + 35 days)
 * @param {string} [req.query.include_production] - Whether to include production data ("true" or "false")
 * 
 * @returns {Object} 200 - Indicator data for requested parameters
 * @returns {Object} 400 - Bad request error for invalid parameters
 * @returns {Object} 401 - Unauthorized error if no valid subscription
 * @returns {Object} 404 - Not found error if no data available
 * @returns {Object} 500 - Internal server error
 */
router.get('/:indicator/:mode/:nuts_id', async (req: AuthRequest, res: Response) => {
    try {
        const { indicator, mode, nuts_id } = req.params;
        const { start_date, end_date, include_production } = req.query;
        const supabase = getSupabaseAdminClient();

        // Validate mode
        if (!['last', 'last24h', 'historical'].includes(mode)) {
            return res.status(400).json({
                message: 'Invalid mode parameter. Valid values are "last", "last24h", "historical".'
            });
        }

        // Validate include_production
        let includeProduction = false;
        if (include_production !== undefined) {
            if (include_production === 'true') {
                includeProduction = true;
            } else if (include_production === 'false') {
                includeProduction = false;
            } else {
                return res.status(400).json({
                    message: 'Invalid value for `include_production`. Valid values are "true" or "false".'
                });
            }
        }

        // Get NUTS codes
        let nutsArray: string[] = [];

        if (nuts_id.toLowerCase() === 'all') {
            const { data, error } = await supabase
                .schema('thales')
                .from('nuts_info')
                .select('nuts_id')
                .eq('is_active', true);

            if (error) {
                console.error('Supabase error:', error);
                return res.status(500).json({
                    message: 'Error fetching active nuts from database. Please try again later or contact support.'
                });
            }
            if (!data || data.length === 0) {
                return res.status(404).json({
                    message: 'No active NUTS codes found.'
                });
            }
            nutsArray = data.map((row) => row.nuts_id.toUpperCase());
        } else if (nuts_id.includes(',')) {
            nutsArray = nuts_id
                .split(',')
                .map((code) => code.trim().toUpperCase())
                .filter((code) => code.length > 0);
        } else {
            nutsArray = [nuts_id.toUpperCase()];
        }

        // Validate subscription
        const subscriptionError = validateSubscription(req, nutsArray);
        if (subscriptionError) {
            return res.status(subscriptionError.status).json(subscriptionError.body);
        }

        // Validate historical access
        if (mode === 'historical') {
            const historicalError = validateHistoricalAccess(req);
            if (historicalError) {
                return res.status(historicalError.status).json(historicalError.body);
            }
        }

        // Get indicator
        const thalesIndicator = (await getThalesIndicators()).find((x) => x.id_short === indicator);
        if (!thalesIndicator) {
            return res.status(404).json({
                message: 'The specified indicator does not match any available indicators. Verify the ID and try again.'
            });
        }

        // Prepare query options
        const influx = getInfluxQueryApi();
        const qOpts: IndicatorQueryOptions = {
            filters: { nuts_id: nutsArray },
            renameTo: 'consumption',
            limit: req.headers['x-swagger-source'] === 'swagger-ui'
                ? 5
                : ['last24h', "last"].includes(mode)
                    ? 96
                    : nuts_id === 'all' ? 480 : undefined,
            getLast: mode === 'last' ? true : ['last24h', 'historical'].includes(mode) ? false : undefined,
        };

        let measurement: string | string[] = 'consumption';

        // Handle different modes
        if (mode === 'last' || mode === 'last24h') {
            // Try cache for single code
            if (nutsArray.length === 1) {
                const singleNutsId = nutsArray[0];
                const dataPoints = dataCache.getData(indicator, singleNutsId, mode);
                const shouldBypassCache =
                    mode === 'last24h' && dataPoints && dataPoints.length < 96;

                if (!shouldBypassCache && dataPoints && dataPoints.length > 0) {
                    return res.status(200).json({
                        indicator: thalesIndicator.id_short,
                        units: thalesIndicator.units,
                        methodology: thalesIndicator.methodology,
                        mode,
                        nuts_id: singleNutsId,
                        data: dataPoints.map((dp) => ({
                            dt: dp.dt instanceof Date ? parseAndFormatDate(dp.dt) : dp.dt,
                            consumption: dp.consumption,
                            production: dp.production
                        }))
                    });
                }
            }

            if (includeProduction) {
                measurement = ['consumption', 'production'];
                qOpts.renameTo = 'value';
                qOpts.returnMeasurement = true;
            }

            // Fetch data
            let finalResults: InfluxDataPoint[];
            if (nutsArray.length > 10) {
                finalResults = await fetchChunkedData(
                    influx,
                    thalesIndicator.id,
                    measurement,
                    qOpts,
                    nutsArray,
                    50
                );
            } else {
                const query = generateIndicatorQuery(thalesIndicator.id, measurement, qOpts);
                finalResults = await influx.collectRows(query);
                finalResults = finalResults.map(({ result, table, ...rest }) => rest);
            }

            // Process results
            const groupedData = groupByKey(finalResults, 'nuts_id');

            if (includeProduction) {
                for (const [k, v] of Object.entries(groupedData)) {
                    groupedData[k] = transformConsProdData(v as InfluxDataPoint[]);
                }
            }

            // Return response
            if (nutsArray.length === 1) {
                const singleCode = nutsArray[0];
                const response = buildSingleResponse(
                    groupedData[singleCode],
                    mode as Mode,
                    singleCode,
                    thalesIndicator,
                    includeProduction,
                    'impacts'
                );

                if (response && Array.isArray(response.data) && response.data.length > 96) {
                    response.data = response.data.slice(0, 96);
                }

                if (!response) {
                    return res.status(404).json({
                        message: 'No data available for the specified parameters.'
                    });
                }

                return res.status(200).json(response);
            }

            const finalDataDict = buildDataDictionary(groupedData, nutsArray, includeProduction, 'impacts');
            const allValues = Object.values(finalDataDict).flat();
            if (allValues.length === 0) {
                return res.status(404).json({
                    message: 'No data available for the specified parameters.'
                });
            }

            return res.status(200).json({
                id: thalesIndicator.id_short,
                indicator: thalesIndicator.indicator_extended,
                units: thalesIndicator.units,
                mode,
                nuts_id: nutsArray,
                data: finalDataDict
            });
        }

        // Historical mode
        else if (mode === 'historical') {
            if (!start_date) {
                return res.status(400).json({
                    message: 'Start date for historical data is required (start_date).'
                });
            }

            const startDateParam = Array.isArray(start_date) ? start_date[0] : start_date;
            const startDate = validateDate(startDateParam as string);
            if (!startDate) {
                return res.status(400).json({
                    message: 'Invalid start_date. Must be a valid date in ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).'
                });
            }

            const endDateParam = Array.isArray(end_date) ? end_date[0] : end_date;
            let endDate: Date;
            if (endDateParam === undefined) {
                endDate = new Date(startDate.getTime() + 35 * 24 * 60 * 60 * 1000 - 1);
            } else {
                const validatedEndDate = validateDate(endDateParam as string);
                if (!validatedEndDate) {
                    return res.status(400).json({
                        message: 'Invalid end_date. Must be a valid date in ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).'
                    });
                }
                endDate = validatedEndDate;
            }

            const now = new Date();
            if (endDate.getTime() > now.getTime()) {
                endDate = now;
            }

            qOpts.start = parseAndFormatDate(startDate);
            qOpts.end = parseAndFormatDate(endDate);

            if (req.headers['x-swagger-source'] === 'swagger-ui') {
                qOpts.limit = 5;
            }

            if (includeProduction) {
                measurement = ['consumption', 'production'];
                qOpts.renameTo = 'value';
                qOpts.returnMeasurement = true;
            }

            // Fetch data
            let finalResults: InfluxDataPoint[] = [];
            if (nutsArray.length > 10) {
                finalResults = await fetchChunkedData(
                    influx,
                    thalesIndicator.id,
                    measurement,
                    qOpts,
                    nutsArray,
                    50
                );
            } else {
                const query = generateIndicatorQuery(thalesIndicator.id, measurement, qOpts);
                finalResults = await influx.collectRows(query);
                finalResults = finalResults.map(({ result, table, ...rest }) => rest);
            }

            // Process results
            const groupedData = groupByKey(finalResults, 'nuts_id');
            if (includeProduction) {
                for (const [k, v] of Object.entries(groupedData)) {
                    groupedData[k] = transformConsProdData(v as InfluxDataPoint[]);
                }
            }

            // Return response
            if (nutsArray.length === 1) {
                const singleCode = nutsArray[0];
                const response = buildSingleResponse(
                    groupedData[singleCode],
                    mode as Mode,
                    singleCode,
                    thalesIndicator,
                    includeProduction,
                    'impacts'
                );

                if (!response) {
                    return res.status(404).json({
                        message: 'No data available for the specified parameters.'
                    });
                }

                return res.status(200).json(response);
            }

            const finalDataDict = buildDataDictionary(groupedData, nutsArray, includeProduction, 'impacts');
            const allValues = Object.values(finalDataDict).flat();

            if (allValues.length === 0) {
                return res.status(404).json({
                    message: 'No data available for the specified parameters.'
                });
            }

            return res.status(200).json({
                id: thalesIndicator.id_short,
                indicator: thalesIndicator.indicator_extended,
                units: thalesIndicator.units,
                mode,
                nuts_id: nutsArray,
                data: finalDataDict
            });
        }
    } catch (error) {
        console.error('Internal server error:', error);
        return res.status(500).json({
            message: 'An internal server error occurred. Please try again later or contact support if the issue persists.'
        });
    }
});

/**
 * 
 * @route GET /indicators/:indicator/exclude-inactive
 * @description Retrieves indicator data for a specific indicator, excluding inactive NUTS regions.
 * 
 * @param {AuthRequest} req - Express request object with authentication data
 * @param {Response} res - Express response object
 * @param {string} req.params.indicator - Indicator ID to retrieve
 * 
 * @returns {Object} 200 - Indicator data excluding inactive regions
 * @returns {Object} 401 - Unauthorized error if no valid subscription
 * @returns {Object} 404 - Not found error if no data available
 * @returns {Object} 500 - Internal server error
 */
router.get('/:indicator/exclude-inactive', async (req: AuthRequest, res: Response) => {
    try {
        const { indicator } = req.params;
        const supabase = getSupabaseAdminClient();

        // Get inactive NUTS codes
        const { data: inactiveNuts, error: inactiveError } = await supabase
            .schema('thales')
            .from('nuts_info')
            .select('nuts_id')
            .eq('is_active', false);

        if (inactiveError) {
            console.error('Supabase error:', inactiveError);
            return res.status(500).json({
                message: 'Error fetching inactive nuts from database. Please try again later or contact support.'
            });
        }

        const inactiveNutsIds = inactiveNuts?.map(row => row.nuts_id.toUpperCase()) || [];

        // Get indicator
        const thalesIndicator = (await getThalesIndicators()).find((x) => x.id_short === indicator);
        if (!thalesIndicator) {
            return res.status(404).json({
                message: 'The specified indicator does not match any available indicators. Verify the ID and try again.'
            });
        }

        // Prepare query options
        const influx = getInfluxQueryApi();
        const query = generateExcludeInactiveIndicatorQuery(thalesIndicator.id, ['consumption', 'production'], {
            filters: {
                nuts_id: inactiveNutsIds.length > 0
                    ? [`!~ /^(?!(${inactiveNutsIds.map(id => id.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|')})$).*$/`]
                    : ['all']
            },
            renameTo: 'value',
            returnMeasurement: true,
            limit: 96, // Default to last 24h of data
            getLast: false,
        });

        // Execute query
        const result = await influx.collectRows(query);
        const influxData = result.map((row: any) => {
            const { result, table, ...rest } = row;
            return rest;
        }) as InfluxDataPoint[];

        // Transform data while preserving nuts_id
        const transformedMap = new Map<string, {
            dt: string;
            nuts_id: string;
            consumption: number | null;
            production: number | null;
        }>();

        influxData.forEach(point => {
            const key = `${point.dt}-${point.nuts_id}`;
            const existing = transformedMap.get(key) || {
                dt: point.dt,
                nuts_id: point.nuts_id,
                consumption: null,
                production: null
            };

            if (point._measurement === 'consumption') {
                existing.consumption = point.value;
            } else if (point._measurement === 'production') {
                existing.production = point.value;
            }

            transformedMap.set(key, existing);
        });

        const transformedData = Array.from(transformedMap.values())
            .sort((a, b) => new Date(a.dt).getTime() - new Date(b.dt).getTime());

        // Group data by nuts_id
        const groupedData = groupByKey(transformedData, 'nuts_id');

        // Get unique nuts_ids from the data
        const nutsIds = Object.keys(groupedData);

        res.status(200).json({
            indicator: thalesIndicator.id_short,
            units: thalesIndicator.units,
            methodology: thalesIndicator.methodology,
            nuts_id: nutsIds,
            data: groupedData
        });
    } catch (error) {
        console.error('Internal server error:', error);
        res.status(500).json({
            message: 'An internal server error occurred. Please try again later or contact support if the issue persists.'
        });
    }
});

export default router;

