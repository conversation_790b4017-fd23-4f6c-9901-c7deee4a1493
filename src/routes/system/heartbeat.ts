import { Router, Response } from 'express';
import { AuthRequest } from '../../middleware/auth';
import { getInfluxQueryApi } from '../../influxdb/influxClient';
import { getThalesClient } from '../../supabaseClient';

const router = Router();

router.get('/heartbeat', async (req: AuthRequest, res: Response) => {
    if (!req.subscription) {
        return res.status(401).json({
            status: 'error',
            code: 401,
            message:
                'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
        });
    }

    try {
        const thalesClient = getThalesClient();
        const { data: supabaseData, error: supabaseError } = await thalesClient
            .from('environmental_indicators')
            .select('id')
            .limit(1);

        if (supabaseError) {
            throw new Error(`Supabase error: ${supabaseError.message}`);
        }

        const queryApi = getInfluxQueryApi();
        const defaultBucket = process.env.INFLUX_DEFAULT_BUCKET || 'emissium';

        const fluxQuery = `
            from(bucket: "${defaultBucket}")
                |> range(start: -1m)
                |> limit(n: 1)
        `;

        await new Promise<void>((resolve, reject) => {
            queryApi.queryRows(fluxQuery, {
                next: (row, tableMeta) => {
                    // For connectivity check, we don't need to process data, hence leave this empty.
                },
                error: (error) => {
                    reject(error);
                },
                complete: () => {
                    resolve();
                }
            });
        });

        const successResponse = {
            status: 'success',
            code: 200,
            message: 'System is responding and database connections are available.'
        };

        res.status(200).json(successResponse);
    } catch (error) {
        const isProduction = process.env.NODE_ENV === 'production';
        const errMsg = error instanceof Error ? error.message : String(error);
        res.status(500).json({
            status: 'error',
            code: 500,
            message:
                'System is not responding properly. This could indicate system downtime or issues with database connectivity. For further assistance, please contact support.',
            error: isProduction ? undefined : errMsg
        });
    }
});

export default router;
