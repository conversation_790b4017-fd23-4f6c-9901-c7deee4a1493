import { Router, Request, Response } from 'express';
import { AuthRequest } from '../../middleware/auth';

const router = Router();

/**
 * @typedef {object} ZoneData
 * @property {number} value - The value of the indicator
 * @property {string} trend - The trend of the indicator
 */

/**
 * @typedef {object} RankingResponse
 * @property {string} timestamp - The current UTC time
 * @property {object} consumption - The consumption data
 * @property {object.<string, ZoneData>} consumption.top - The top-ranked zones
 * @property {object.<string, ZoneData>} consumption.bottom - The bottom-ranked zones
 */

/**
 * GET /v1/stats/{indicator}/ranking
 * @summary Returns the zones where the selected indicator is currently the highest and lowest
 * @tags Stats - Pro
 * @security BearerAuth
 * @param {string} indicator.path - The indicator for the data
 * @param {integer} [top_count.query] - Number of top-ranked zones to include in the response
 * @param {integer} [bottom_count.query] - Number of bottom-ranked zones to include in the response
 * @return {object} 200 - success response - application/json
 * @return {object} 400 - Bad request
 * @return {object} 404 - Not found
 */
// Extract and validate the path parameter
router.get('/stats/:indicator/ranking', async (req: AuthRequest, res: Response) => {
    const { indicator } = req.params;
    const { top_count = 5, bottom_count = 5 } = req.query;

    // Add your logic to fetch data from the database here
    // Example response:
    const response = {
        indicator,
        timestamp: new Date().toISOString(),
        consumption: {
            top: {
                ME: {
                    value: 1049.0,
                    trend: 'increasing'
                }
                // Add more top zones data here
            },
            bottom: {
                DE: {
                    value: 50.0,
                    trend: 'increasing'
                }
            }
        }
    };

    res.json(response);
});

export default router;
