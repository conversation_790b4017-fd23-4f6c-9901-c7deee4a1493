import { Router, Response } from 'express';
import { AuthRequest } from "../middleware/auth";
import { getSupabaseAdminClient } from '../supabaseClient';
import { fetchChunkedData, validateDate, validateHistoricalAccess, parseAndFormatDate } from '../utils/utils';
import { validateSubscription } from '../utils/utils';
import { getInfluxQueryApi } from '../influxdb/influxClient';
import { generateIndicatorQuery, IndicatorQueryOptions } from '../influxdb/queries';
import { InfluxDataPoint } from '../utils/dataTransforms';
import { DataPoint } from '../utils/types';

const router = Router();

/**
 * @route GET /power/:mode/:nuts_id
 * @description Retrieves power consumption or production data for specified NUTS regions.
 * 
 * @param {string} mode - The time range mode: 'last' (most recent data point), 'last24h' (last 24 hours), or 'historical' (custom date range)
 * @param {string} nuts_id - NUTS region ID(s). Can be a single ID, comma-separated list, or 'all' for all active regions
 * @param {string} [start_date] - Required for historical mode. Start date in ISO8601 format (yyyy-mm-ddTHH:MM:SSZ)
 * @param {string} [end_date] - Optional for historical mode. End date in ISO8601 format. Defaults to start_date + 35 days
 * @param {string} [type=consumption] - Data type: 'consumption' or 'production'
 * 
 * @returns {Object} Response containing power data:
 *   - For single region: Object with metadata and array of data points
 *   - For multiple regions: Object with metadata and record of region IDs to data point arrays
 *   - Each data point includes timestamp, value (kWh), and per-capita value when population data is available
 * 
 * @throws {400} - Invalid parameters
 * @throws {401} - Unauthorized access
 * @throws {403} - Subscription limitations
 * @throws {404} - No data available
 * @throws {500} - Server error
 */
router.get('/:mode/:nuts_id', async (req: AuthRequest, res: Response) => {
    try {
        const { mode, nuts_id } = req.params;
        const { start_date, end_date, type = 'consumption' } = req.query;

        // Validate mode
        if (!['last', 'last24h', 'historical'].includes(mode)) {
            return res.status(400).json({
                message: 'Invalid mode parameter. Valid values are "last", "last24h", "historical".'
            });
        }

        // Validate type
        if (!['consumption', 'production'].includes(type as string)) {
            return res.status(400).json({
                message: 'Invalid type parameter. Valid values are "consumption" or "production".'
            });
        }

        // Get NUTS codes
        const supabase = getSupabaseAdminClient();
        let nutsArray: string[] = [];

        if (nuts_id.toLowerCase() === 'all') {
            const { data, error } = await supabase
                .schema('thales')
                .from('nuts_info')
                .select('nuts_id')
                .eq('is_active', true);

            if (error) {
                console.error('Supabase error:', error);
                return res.status(500).json({
                    message: 'Error fetching active nuts from database. Please try again later or contact support.'
                });
            }
            if (!data || data.length === 0) {
                return res.status(404).json({
                    message: 'No active NUTS codes found.'
                });
            }
            nutsArray = data.map((row) => row.nuts_id.toUpperCase());
        } else if (nuts_id.includes(',')) {
            nutsArray = nuts_id
                .split(',')
                .map((code) => code.trim().toUpperCase())
                .filter((code) => code.length > 0);
        } else {
            nutsArray = [nuts_id.toUpperCase()];
        }

        // Validate subscription
        const subscriptionError = validateSubscription(req, nutsArray);
        if (subscriptionError) {
            return res.status(subscriptionError.status).json(subscriptionError.body);
        }

        // Validate historical access
        if (mode === 'historical') {
            const historicalError = validateHistoricalAccess(req);
            if (historicalError) {
                return res.status(historicalError.status).json(historicalError.body);
            }
        }

        // Prepare query options
        const influx = getInfluxQueryApi();
        const qOpts: IndicatorQueryOptions = {
            filters: { nuts_id: nutsArray },
            renameTo: 'power',
            limit: req.headers['x-swagger-source'] === 'swagger-ui' 
                ? 5 
                : mode === 'last'
                    ? 1 
                    : mode === 'last24h'
                        ? 96 
                        : nuts_id === 'all' ? 480 : undefined,
            getLast: mode === 'last' ? true : ['last24h', 'historical'].includes(mode) ? false : undefined,
        };

        // Handle different modes
        if (mode === 'last' || mode === 'last24h') {
            // Fetch data
            let finalResults: InfluxDataPoint[];
            if (nutsArray.length > 10) {
                finalResults = await fetchChunkedData(
                    influx,
                    'power',
                    type as string,
                    qOpts,
                    nutsArray,
                    50
                );
            } else {
                const query = generateIndicatorQuery('power', type as string, qOpts);
                finalResults = await influx.collectRows(query);
                finalResults = finalResults.map(({ result, table, ...rest }) => rest);
            }

            // Process results
            const timeSeriesData: Record<string, DataPoint[]> = {};
            
            // Get population data
            const { data: populationData, error: populationError } = await supabase
                .schema('thales')
                .from('nuts_metadata')
                .select('nuts_id, population')
                .in('nuts_id', nutsArray);

            if (populationError) {
                console.error('Error fetching population data:', populationError);
                throw new Error('Error fetching population data');
            }

            const populationMap = new Map(
                populationData?.map(d => [d.nuts_id, d.population ? parseFloat(d.population) : null]) || []
            );
            
            // Process data points
            for (const result of finalResults) {
                const code = result.nuts_id;
                const value = result.power;
                const dt = result.dt;
                
                if (value !== null && value !== undefined && !isNaN(value) && dt) {
                    if (!timeSeriesData[code]) {
                        timeSeriesData[code] = [];
                    }
                    const population = populationMap.get(code);
                    const dataPoint: DataPoint = {
                        dt: parseAndFormatDate(dt),
                        value: Number(value.toFixed(2))
                    };
                    
                    if (population && !isNaN(population) && population > 0) {
                        const perCapitaValue = value / population;
                        dataPoint.value_per_capita = Number(perCapitaValue.toFixed(6));
                    }
                    
                    timeSeriesData[code].push(dataPoint);
                }
            }

            // Return response
            if (nutsArray.length === 1) {
                const singleCode = nutsArray[0];
                const data = timeSeriesData[singleCode];

                if (!data || data.length === 0) {
                    return res.status(404).json({
                        message: 'No data available for the specified parameters.'
                    });
                }

                return res.status(200).json({
                    id: 'power',
                    indicator: `Power ${type === 'consumption' ? 'Consumption' : 'Production'}`,
                    units: 'MW',
                    mode,
                    nuts_id: singleCode,
                    data: data,
                    type
                });
            }

            return res.status(200).json({
                id: 'power',
                indicator: `Power ${type === 'consumption' ? 'Consumption' : 'Production'}`,
                units: 'MW',
                mode,
                nuts_id: nutsArray,
                data: timeSeriesData,
                type
            });
        }

        // Historical mode
        else if (mode === 'historical') {
            if (!start_date) {
                return res.status(400).json({
                    message: 'Start date for historical data is required (start_date).'
                });
            }

            const startDateParam = Array.isArray(start_date) ? start_date[0] : start_date;
            const startDate = validateDate(startDateParam as string);
            if (!startDate) {
                return res.status(400).json({
                    message: 'Invalid start_date. Must be a valid date in ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).'
                });
            }

            const endDateParam = Array.isArray(end_date) ? end_date[0] : end_date;
            let endDate: Date;
            if (endDateParam === undefined) {
                endDate = new Date(startDate.getTime() + 35 * 24 * 60 * 60 * 1000 - 1);
            } else {
                const validatedEndDate = validateDate(endDateParam as string);
                if (!validatedEndDate) {
                    return res.status(400).json({
                        message: 'Invalid end_date. Must be a valid date in ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).'
                    });
                }
                endDate = validatedEndDate;
            }

            const now = new Date();
            if (endDate.getTime() > now.getTime()) {
                endDate = now;
            }

            qOpts.start = parseAndFormatDate(startDate);
            qOpts.end = parseAndFormatDate(endDate);

            // Fetch data
            let finalResults: InfluxDataPoint[];
            if (nutsArray.length > 10) {
                finalResults = await fetchChunkedData(
                    influx,
                    'power',
                    type as string,
                    qOpts,
                    nutsArray,
                    50
                );
            } else {
                const query = generateIndicatorQuery('power', type as string, qOpts);
                finalResults = await influx.collectRows(query);
                finalResults = finalResults.map(({ result, table, ...rest }) => rest);
            }

            // Process results
            const timeSeriesData: Record<string, DataPoint[]> = {};
            
            // Get population data
            const { data: populationData, error: populationError } = await supabase
                .schema('thales')
                .from('nuts_metadata')
                .select('nuts_id, population')
                .in('nuts_id', nutsArray);

            if (populationError) {
                console.error('Error fetching population data:', populationError);
                throw new Error('Error fetching population data');
            }

            const populationMap = new Map(
                populationData?.map(d => [d.nuts_id, d.population ? parseFloat(d.population) : null]) || []
            );
            
            // Process data points
            for (const result of finalResults) {
                const code = result.nuts_id;
                const value = result.power;
                const dt = result.dt;
                
                if (value !== null && value !== undefined && !isNaN(value) && dt) {
                    if (!timeSeriesData[code]) {
                        timeSeriesData[code] = [];
                    }
                    const population = populationMap.get(code);
                    const dataPoint: DataPoint = {
                        dt: parseAndFormatDate(dt),
                        value: Number(value.toFixed(2))
                    };
                    
                    if (population && !isNaN(population) && population > 0) {
                        const perCapitaValue = value / population;
                        dataPoint.value_per_capita = Number(perCapitaValue.toFixed(6));
                    }
                    
                    timeSeriesData[code].push(dataPoint);
                }
            }

            // Return response
            if (nutsArray.length === 1) {
                const singleCode = nutsArray[0];
                const data = timeSeriesData[singleCode];

                if (!data || data.length === 0) {
                    return res.status(404).json({
                        message: 'No data available for the specified parameters.'
                    });
                }

                return res.status(200).json({
                    id: 'power',
                    indicator: `Power ${type === 'consumption' ? 'Consumption' : 'Production'}`,
                    units: 'MW',
                    mode,
                    nuts_id: singleCode,
                    data: data,
                    type
                });
            }

            return res.status(200).json({
                id: 'power',
                indicator: `Power ${type === 'consumption' ? 'Consumption' : 'Production'}`,
                units: 'MW',
                mode,
                nuts_id: nutsArray,
                data: timeSeriesData,
                type
            });
        }
    } catch (error) {
        console.error('Internal server error:', error);
        return res.status(500).json({
            message: 'An internal server error occurred. Please try again later or contact support if the issue persists.'
        });
    }
});

export default router;