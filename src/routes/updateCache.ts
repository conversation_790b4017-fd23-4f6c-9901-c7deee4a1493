import { Router, Response } from 'express';
import dataCache, { DataPoint } from '../cache/dataCache';
import { AuthRequest } from '../middleware/auth';
import { getSupabaseAdminClient } from '../supabaseClient';

const router = Router();

interface CacheUpdateRequest {
    indicator: string;
    units: string;
    data: {
        [nuts_id: string]: Array<{
            dt: string; // ISO string
            consumption: number;
            production: number;
        }>;
    };
}

router.post('/', async (req: AuthRequest, res: Response) => {
    try {
        const body: CacheUpdateRequest = req.body;
        const { indicator, units, data } = body;

        if (!indicator || !units || !data || typeof data !== 'object') {
            return res.status(400).json({ message: 'Invalid request body' });
        }

        const supabase = getSupabaseAdminClient();

        // Fetch valid locations
        const { data: validLocations, error: locationsError } = await supabase
            .from('subscription_locations')
            .select('location_id');

        if (locationsError) {
            console.error('Error fetching valid locations:', locationsError);
            return res.status(500).json({ message: 'Error updating cache', error: locationsError });
        }

        const validNutsIds = new Set(validLocations.map((loc) => loc.location_id.toUpperCase()));

        // Verify the indicator actually exists:
        const { data: indicatorData, error: indicatorError } = await supabase
            .schema('thales')
            .from('environmental_indicators')
            .select('id')
            .eq('id_short', indicator)
            .single();

        if (indicatorError || !indicatorData) {
            console.error('Error fetching indicator:', indicatorError);
            return res.status(400).json({ message: 'Invalid indicator', error: indicatorError });
        }

        // Store data in cache as is
        for (const nutsId in data) {
            const upperNutsId = nutsId.toUpperCase();
            if (!validNutsIds.has(upperNutsId) && req.isGod !== true) {
                console.log(`Skipping ${nutsId}, not in validLocations`);
                continue;
            }

            let dataPointsArray = data[nutsId];

            // Handle case where data[nutsId] is a single object instead of an array
            if (!Array.isArray(dataPointsArray)) {
                if (typeof dataPointsArray === 'object' && dataPointsArray !== null) {
                    dataPointsArray = [dataPointsArray];
                } else {
                    console.warn(`Invalid dataPointsArray for ${nutsId}`);
                    continue;
                }
            }

            for (const item of dataPointsArray) {
                const timestamp = new Date(item.dt);
                if (isNaN(timestamp.getTime())) {
                    console.warn(`Invalid timestamp: ${item.dt}`);
                    continue;
                }

                const dataPoint: DataPoint = {
                    dt: timestamp,
                    consumption: item.consumption,
                    production: item.production
                };
                dataCache.put(indicator, upperNutsId, timestamp, dataPoint);

                // use this for debugging
                // const dtCache = dataCache.get(indicator, upperNutsId);
                // if (dtCache) {
                //     console.log(
                //         `[DEBUG] Inserted data for ${indicator}/${upperNutsId} at ${timestamp.toISOString()}`
                //     );
                //     console.log('[DEBUG] All keys in this cache:', dtCache.getAllKeys());
                //     console.log('[DEBUG] Most recent data point:', dtCache.getMostRecent());
                // } else {
                //     console.warn(
                //         `[WARN] No cache entry found for ${indicator}/${upperNutsId} after insertion.`
                //     );
                // }
            }
        }

        return res.status(200).json({ message: 'Cache updated successfully' });
    } catch (error) {
        console.error('Error updating cache:', error);
        return res.status(500).json({ message: 'Error updating cache', error });
    }
});

export default router;
