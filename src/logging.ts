import fs from 'fs';
import path from 'path';
import { Express, Request } from 'express';
import morgan from 'morgan';
import { createStream } from 'rotating-file-stream';
// Create custom token to log request body
morgan.token('body', (req: Request) => JSON.stringify(req.body));

// Create custom token to log authorization header
morgan.token('auth', (req: Request) => req.headers['authorization'] || '');
// Create custom token to log request time in ISO 8601 format
morgan.token('iso-date', () => new Date().toISOString());
export const setupLogging = (app: Express) => {
    // Custom log directory
    // Setup morgan logger

    if (process.env.NODE_ENV === 'production') {
        const logDirectory = path.join(__dirname, '..', 'log');

        // Ensure log directory exists
        fs.existsSync(logDirectory) || fs.mkdirSync(logDirectory);

        // Create a rotating write stream (in append mode)
        const accessLogStream = createStream('access.log', {
            interval: '1d', // Rotate daily
            path: logDirectory,
            maxFiles: 7 // Retain logs for 7 days
        });

        const prodLogs =
            ':iso-date :method :url :status  :response-time ms - :res[content-length] :body :auth';

        app.use(morgan(prodLogs, { stream: accessLogStream }));
        app.use(morgan(prodLogs));
    } else {
        const devLogs = ':iso-date body :body auth :auth';
        app.use(morgan('dev'));
        app.use(morgan(devLogs));
    }
};
