import { Express, Request, Response, NextFunction } from 'express';
import path from 'path';
import swaggerUi from 'swagger-ui-express';
import YAML from 'yaml';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();
const PORT = process.env.PORT || 2999;

const getSwaggerDocument = (isProd: boolean, hostname: string) => {
    const swaggerFilePath =
        isProd && hostname === 'data.emissium.io'
            ? path.join(__dirname, '../main-swagger.yml')
            : path.join(__dirname, '../dev-swagger.yml');

    const file = fs.readFileSync(swaggerFilePath, 'utf8');
    return YAML.parse(file);
};

const setupSwagger = (app: Express) => {
    const isProd = process.env.NODE_ENV === 'production';

    const swaggerOptions = {
        customSiteTitle: 'Emissium API Documentation',
        customCss: '.swagger-ui .topbar { display: none }',
        swaggerOptions: {
            requestInterceptor: (req: any) => {
                req.headers['X-Swagger-Source'] = 'swagger-ui';
                return req;
            }
        }
    };

    app.use('/docs/public', swaggerUi.serve, (req: Request, res: Response, next: NextFunction) => {
        const hostname = req.hostname;

        let serverUrl: string;
        if (hostname === 'data.emissium.io') {
            serverUrl = 'https://data.emissium.io/api/v1';
        } else if (hostname === 'test.data.emissium.io') {
            serverUrl = 'https://test.data.emissium.io/api/v1';
        } else if (!isProd) {
            // Dev environment
            serverUrl = `http://localhost:${PORT}/api/v1`;
        } else {
            // Fallback for other environments if needed
            serverUrl = `https://${hostname}/api/v1`;
        }

        const swaggerDocument = getSwaggerDocument(isProd, hostname);
        const modifiedSwaggerDocument = {
            ...swaggerDocument,
            servers: [
                {
                    url: serverUrl
                    // Optionally add descriptions
                    // description: isProd ? 'Production server' : 'Development server'
                }
            ]
        };

        swaggerUi.setup(modifiedSwaggerDocument, swaggerOptions)(req, res, next);
    });

    console.log(`Swagger docs available at http://127.0.0.1:${PORT}/docs/public`);
};

export default setupSwagger;
