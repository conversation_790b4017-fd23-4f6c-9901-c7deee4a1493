config:
  target: "https://data.emissium.io"
  processor: "./load-test-helpers.js"
  defaults:
    headers:
      Authorization: "Bearer em_qwertyuiopasdfghjklzxcvbnm123"
      Content-Type: "application/json"
  plugins:
    metrics-by-endpoint: {}
  ensure:
    maxErrorRate: 25  # Higher threshold for spike tests
  useOnlyRequestNames: true
  phases:
    # Phase 1: Baseline
    - duration: 30
      arrivalRate: 10
      name: "Baseline - Normal traffic"

    # Phase 2: Small spike
    - duration: 30
      arrivalRate: 50
      name: "Small spike - 5x normal load"

    # Phase 3: Recovery
    - duration: 60
      arrivalRate: 10
      name: "First recovery period"

    # Phase 4: Large spike
    - duration: 30
      arrivalRate: 100
      name: "Large spike - 10x normal load"

    # Phase 5: Final recovery
    - duration: 60
      arrivalRate: 10
      name: "Final recovery period"

scenarios:
  - name: "API Spike Test"
    beforeScenario: ["setDefaultHeadersIfNeeded", "selectRandomTestValues"]
    flow:
      # System heartbeat (lightweight)
      - get:
          name: "SPIKE_system_heartbeat"
          url: "/api/v1/system/heartbeat"
          expect:
            - statusCode: [200, 429, 503]
          afterResponse: "logResponse"
      
      # Get specific zone (medium)
      - get:
          name: "SPIKE_specific_zone"
          url: "/api/v1/zones/{{ randomNutsId }}"
          expect:
            - statusCode: [200, 429, 503]
          afterResponse: "logResponse"
      
      # Get impact data (heavy)
      - get:
          name: "SPIKE_impact_data"
          url: "/api/v1/impacts/{{ randomIndicator }}/{{ randomMode }}/{{ batchNutsIds }}"
          expect:
            - statusCode: [200, 429, 503]
          afterResponse: "logResponse"
      
      # Get all indicators (medium)
      - get:
          name: "SPIKE_indicators"
          url: "/api/v1/indicators"
          expect:
            - statusCode: [200, 429, 503]
          afterResponse: "logResponse"
      
      # Minimal think time
      - think: 0.1 
