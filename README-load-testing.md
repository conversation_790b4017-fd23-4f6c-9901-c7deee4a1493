# API Load Testing Guide

This guide explains how to run load tests against the Emissium API using Artillery.

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set your API token as an environment variable:
   ```bash
   export API_TOKEN=your_api_token_here
   ```

3. Start your API server (in a separate terminal):
   ```bash
   npm run dev
   ```

## Running Load Tests

### Individual Endpoint Tests

Each API endpoint category has its own test file:

```bash
# Test system endpoints
npm run load-test:system

# Test zones endpoints
npm run load-test:zones

# Test impacts endpoints
npm run load-test:impacts

# Test indicators endpoints
npm run load-test:indicators

# Test NUTS endpoints
npm run load-test:nuts
```

### Run All Tests

To run all endpoint tests in sequence and store results in Artillery Cloud:

```bash
npm run load-test:all
```

This command will:
1. Run a comprehensive test across all endpoints
2. Record the results to Artillery Cloud for visualization and analysis
3. Provide a link to view the results in the Artillery Cloud dashboard

### Spike Tests

To test how the API handles sudden traffic spikes:

```bash
# Standard spike test
npm run load-test:spike

# Extreme spike test (DDoS simulation)
npm run load-test:extreme
```

### Generate HTML Reports

To generate a detailed HTML report for any test:

```bash
LOAD_TEST_FILE=load-test-zones.yml npm run load-test:report
```

Replace `load-test-zones.yml` with the test file you want to run.

## Artillery Cloud Integration

The `load-test:all` command is configured to send test results to Artillery Cloud, which provides:

- Interactive dashboards for visualizing test results
- Historical test data for comparing performance over time
- Shareable reports for team collaboration
- Advanced analytics for identifying performance bottlenecks

To view your test results:

1. Run the test with `npm run load-test:all`
2. Look for the Artillery Cloud URL in the console output
3. Open the URL to view detailed test results

You can also manually send any test to Artillery Cloud:

```bash
artillery run --record --key a9_SJpkOw4PjIv4hqXMKPr8DE5YeRBlarSW load-test-file.yml
```

## Test Files

- `load-test-system.yml` - Tests system heartbeat endpoint
- `load-test-zones.yml` - Tests zones/NUTS endpoints
- `load-test-impacts.yml` - Tests environmental impact data endpoints
- `load-test-indicators.yml` - Tests environmental indicators endpoints
- `load-test-nuts.yml` - Tests NUTS data endpoints
- `load-test-spike.yml` - Standard spike test
- `load-test-extreme-spike.yml` - Extreme spike test (DDoS simulation)
- `load-test-all.yml` - Combined test for all endpoints (used with Artillery Cloud)

## Configuration

All tests use the following configuration:

- **Target URL**: `http://localhost:2999` by default (override with `API_URL` environment variable)
- **Authentication**: Bearer token (set via `API_TOKEN` environment variable)
- **Metrics**: Detailed metrics by endpoint
- **Phases**: Each test has specific load phases

## Customizing Tests

You can customize the tests by:

1. Modifying the phase durations and arrival rates in the YAML files
2. Adding new endpoints or scenarios
3. Changing the expected responses
4. Updating the helper functions in `load-test-helpers.js`

## Interpreting Results

Artillery provides detailed metrics including:

- Response times (min, max, median, p95, p99)
- Request rates
- HTTP response codes
- Error rates
- Latency by endpoint

Look for:
- High p95/p99 response times
- Increasing error rates under load
- Slow recovery after spikes
- Specific endpoints that perform poorly 