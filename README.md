# Emissium Euclid Api

replacement of emissium-api on express

    - connects to supabase with auth privileges to expose zones
    - connects to influx db to expose indicator data on realtime

#### Gen types from supabase

`npx supabase login
 npx supabase gen types typescript --project-id "afrdxeeulqieluntvmmy" --schema=public,thales> src/types/SupabaseDefinition.d.ts
 npx supabase gen types typescript --project-id "nisedejqfskypolnkdie" --schema=thales> src/types/ThalesDefinition.d.ts
`

#### .env

```
    SUPABASE_URL= "https://afrdxeeulqieluntvmmy.supabase.co"
    SUPABASE_SECRET_KEY = "SECRET_TOKEN"

    SUPABASE_THALES_URL = "https://nisedejqfskypolnkdie.supabase.co"
    SUPABASE_THALES_SECRET_KEY = "SECRET_TOKEN"

    INFLUX_URL = "https://webapp.emissium.net/influxdb"
    INFLUX_TOKEN = "READONLY_TOKEN"
    INFLUX_ORG = "emissium"

```

#### developments

Install:
`pnpm install`

Dev:
`pnpm dev`

Prod:
create a dockercompose.yml like:

````services:
    euclid-api:
        build: .
        ports:
            - '2999:2999'
        environment:
            - NODE_ENV=production
            - PORT=2999
            - SUPABASE_URL=${SUPABASE_URL}
            - SUPABASE_SECRET_KEY=${SUPABASE_SECRET_KEY}
            - INFLUX_URL = "https://webapp.emissium.net/influxdb"
            - INFLUX_TOKEN = {READ_ONLY_TOKEN_IS_FINE}
            - INFLUX_ORG = "emissium"
            - SUPABASE_THALES_URL = "https://nisedejqfskypolnkdie.supabase.co"
            - SUPABASE_THALES_SECRET_KEY = "SECRET_TOKEN"
        volumes:
            - ./log:/usr/src/app/log
        command: npm start```



`docker-compose build`
`docker-compose up`

````

# API Structure Overview

This document provides an overview of the file structure for our API. It is intended to help future developers understand the organization of the codebase and the purpose of each module.

## Root Files

-   **app.ts**: The main entry point of the application. It initializes the server and sets up middleware, routes, and other configurations.
-   **logging.ts**: Handles logging across the application, providing utilities for error, info, and debug logs.
-   **supabaseClient.ts**: Sets up and exports the Supabase client, used for interacting with the Supabase database.
-   **swagger.ts**: Configures Swagger for API documentation, enabling developers to view and interact with the API through a web interface.

## Directories

### influxdb/

-   **influxClient.ts**: Manages the connection to the InfluxDB database, including client setup and configuration.
-   **queries.ts**: Contains queries used to interact with InfluxDB, performing various data retrieval and manipulation tasks.

### middleware/

-   **auth.ts**: Middleware responsible for handling authentication and authorization logic.
-   **handleCors.ts**: Middleware for handling Cross-Origin Resource Sharing (CORS) settings.
-   **rateLimits.ts**: Implements rate limiting to prevent abuse and ensure fair use of the API.

### routes/

-   **geometriesPro.ts**: Handles routes related to geometrical data operations.
-   **indicators.ts**: Routes for dealing with various indicators within the system.
-   **zones.ts**: Manages routes for zone-related data.

#### routes/stats/

-   **statsRoutes.ts**: Routes focused on statistical data, including aggregation and reporting.

#### routes/system/

-   **heartbeat.ts**: A simple route to check the health of the system, typically returning a status or heartbeat response.

### types/

-   **SupabaseDefinition.d.ts**: Type definitions related to the Supabase database.
-   **ThalesDefinition.d.ts**: Type definitions specific to the Thales module or integration.

### utils/

-   **cachedGetters.ts**: Utility functions for caching and retrieving data efficiently.
-   **dataTransforms.ts**: Functions for transforming and processing data as needed by the application.

---

This structure is designed to keep the codebase modular and maintainable, with clear separation of concerns. Each directory and file has a specific role, making it easier for developers to find and understand the code relevant to their tasks.
