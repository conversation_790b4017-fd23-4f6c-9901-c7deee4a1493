# Stage 1: Build the application
FROM node:20-alpine AS build

# Set the working directory
WORKDIR /usr/src/app

# Copy package.json and package-lock.json files
COPY package.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy only the necessary files to build the application
COPY src ./src
COPY tsconfig.json ./
COPY main-swagger.yml ./  
COPY dev-swagger.yml ./

# Build the application
RUN npm run build

# Stage 2: Create the final image with only production dependencies
FROM node:20-alpine

# Set the working directory
WORKDIR /usr/src/app

# Copy package.json and package-lock.json files
COPY package.json ./

# Install only production dependencies
RUN npm install --omit=dev

# Copy built files from the build stage
COPY --from=build /usr/src/app/dist ./dist
COPY --from=build /usr/src/app/main-swagger.yml ./main-swagger.yml
COPY --from=build /usr/src/app/dev-swagger.yml ./dev-swagger.yml

# Expose the port the app runs on
EXPOSE 2999

# Define the command to run the app
CMD ["npm", "start"]
