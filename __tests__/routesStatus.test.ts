import YAML from 'yaml';
import fs from 'fs';
import path from 'path';
import request from 'supertest';
import dotenv from 'dotenv';
import app, { startServer } from '../src/app';
import { AddressInfo } from 'net';

dotenv.config();

let server: any;
let port: number;

beforeAll(async () => {
    server = await startServer(0); // Use port 0 to let the OS assign a free port
    port = (server.address() as AddressInfo).port;
});

afterAll((done) => {
    server.close(done);
});

const BEARER_TOKEN = process.env.BEARER_TOKEN;

describe('API Routes', () => {
    describe('GET /system/heartbeat', () => {
        it('Should return 401 without authentication', async () => {
            const response = await request(app).get('/api/v1/system/heartbeat');
            expect(response.status).toBe(401);
        });

        it('Should return 200 with valid authentication and correct response structure', async () => {
            const response = await request(app)
                .get('/api/v1/system/heartbeat')
                .set('Authorization', `Bearer ${BEARER_TOKEN}`);
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty(
                'message',
                'System is responding and database connections are available.'
            );
        });
    });

    describe('GET /zones', () => {
        it('Should return 401 without authentication', async () => {
            const response = await request(app).get('/api/v1/zones');
            expect(response.status).toBe(401);
        });

        it('Should return 200 with valid authentication and correct response structure', async () => {
            const response = await request(app)
                .get('/api/v1/zones')
                .set('Authorization', `Bearer ${BEARER_TOKEN}`);
            expect(response.status).toBe(200);
            expect(Array.isArray(response.body)).toBe(true);
            response.body.forEach((zone: any) => {
                expect(zone).toHaveProperty('nuts_id');
                expect(typeof zone.nuts_id).toBe('string');
                expect(zone).toHaveProperty('name');
                expect(typeof zone.name).toBe('string');
                expect(zone).toHaveProperty('is_active');
                expect(typeof zone.is_active).toBe('boolean');
            });
        });
    });

    describe('GET /zones/:nuts_id', () => {
        const nutsId = 'AT';
        it('Should return 401 without authentication', async () => {
            const response = await request(app).get(`/api/v1/zones/${nutsId}`);
            expect(response.status).toBe(401);
        });

        it('Should return 200 with valid authentication and correct response structure', async () => {
            const response = await request(app)
                .get(`/api/v1/zones/${nutsId}`)
                .set('Authorization', `Bearer ${BEARER_TOKEN}`);
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('nuts_id', nutsId);
            expect(response.body).toHaveProperty('name');
            expect(typeof response.body.name).toBe('string');
            expect(response.body).toHaveProperty('is_active');
            expect(typeof response.body.is_active).toBe('boolean');
            expect(response.body).toHaveProperty('nuts_level');
            expect(typeof response.body.nuts_level).toBe('string');
            expect(response.body).toHaveProperty('children');
            expect(Array.isArray(response.body.children)).toBe(true);
            response.body.children.forEach((child: any) => {
                expect(typeof child === 'string' || typeof child === 'object').toBe(true);
            });
        });
    });

    describe('GET /indicators', () => {
        it('Should return 401 without authentication', async () => {
            const response = await request(app).get('/api/v1/indicators');
            expect(response.status).toBe(401);
        });

        it('Should return 200 with valid authentication and correct response structure', async () => {
            const response = await request(app)
                .get('/api/v1/indicators')
                .set('Authorization', `Bearer ${BEARER_TOKEN}`);
            expect(response.status).toBe(200);
            expect(Array.isArray(response.body)).toBe(true);
            response.body.forEach((indicator: any) => {
                expect(indicator).toHaveProperty('id');
                expect(typeof indicator.id).toBe('string');
                expect(indicator).toHaveProperty('methodology');
                expect(typeof indicator.methodology).toBe('string');
                expect(indicator).toHaveProperty('units');
                expect(typeof indicator.units).toBe('string');
                expect(indicator).toHaveProperty('is_available');
                expect(typeof indicator.is_available).toBe('boolean');
            });
        });
    });

    describe('GET /indicators/:indicator/:mode/:nuts_id', () => {
        const indicator = 'gwp100a';
        const mode = 'last';
        const nutsId = 'AT';
        it('Should return 401 without authentication', async () => {
            const response = await request(app).get(`/api/v1/indicators/${indicator}/${mode}/${nutsId}`);
            expect(response.status).toBe(401);
        });

        it('Should return 200 with valid authentication and correct response structure', async () => {
            const response = await request(app)
                .get(`/api/v1/indicators/${indicator}/${mode}/${nutsId}`)
                .set('Authorization', `Bearer ${BEARER_TOKEN}`);
            expect(response.status).toBe(200);

            expect(response.body).toHaveProperty('indicator', indicator);
            expect(response.body).toHaveProperty('units');
            expect(typeof response.body.units).toBe('string');
            expect(response.body).toHaveProperty('methodology');
            expect(typeof response.body.methodology).toBe('string');
            expect(response.body).toHaveProperty('mode', mode);
            expect(response.body).toHaveProperty('nuts_id', nutsId);
            expect(response.body).toHaveProperty('data');
            expect(typeof response.body.data).toBe('object');

            const data = response.body.data;
            expect(data).toHaveProperty(nutsId);
            expect(Array.isArray(data[nutsId])).toBe(true);
            data[nutsId].forEach((entry: any) => {
                expect(entry).toHaveProperty('dt');
                expect(new Date(entry.dt).toString()).not.toBe('Invalid Date');
                expect(entry).toHaveProperty('consumption');
                expect(typeof entry.consumption).toBe('number');
            });
        });
    });

    describe('GET /geometries', () => {
        it('Should return 401 without authentication', async () => {
            const response = await request(app).get('/api/v1/geometries');
            expect(response.status).toBe(401);
        });

        it('Should return 200 with valid authentication and correct response structure', async () => {
            const response = await request(app)
                .get('/api/v1/geometries')
                .set('Authorization', `Bearer ${BEARER_TOKEN}`);
            expect(response.status).toBe(200);
            expect(Array.isArray(response.body)).toBe(true);
            response.body.forEach((geometry: any) => {
                expect(geometry).toHaveProperty('nuts_id');
                expect(typeof geometry.nuts_id).toBe('string');
                expect(geometry).toHaveProperty('nuts_geometries');
                expect(geometry.nuts_geometries).toHaveProperty('geometry');
                expect(typeof geometry.nuts_geometries.geometry).toBe('object');
            });
        });
    });

    describe('GET /geometries/:nuts_id', () => {
        const nutsId = 'AT';
        it('Should return 401 without authentication', async () => {
            const response = await request(app).get(`/api/v1/geometries/${nutsId}`);
            expect(response.status).toBe(401);
        });

        it('Should return 200 with valid authentication and correct response structure', async () => {
            const response = await request(app)
                .get(`/api/v1/geometries/${nutsId}`)
                .set('Authorization', `Bearer ${BEARER_TOKEN}`);
            expect(response.status).toBe(200);

            expect(response.body).toHaveProperty('nuts_id', nutsId);
            expect(response.body).toHaveProperty('geometry');
            expect(typeof response.body.geometry).toBe('object');
        });
    });
});
