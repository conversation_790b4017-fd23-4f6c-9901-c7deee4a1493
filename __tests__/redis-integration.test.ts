import { jest } from '@jest/globals';

// Mock Redis client before importing modules
const mockRedisClient = {
  isOpen: false,
  connect: jest.fn().mockResolvedValue(undefined),
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  flushAll: jest.fn(),
  info: jest.fn().mockResolvedValue('db0:keys=0\nused_memory_human:1M\nrdb_last_save_time:1640995200\nconnected_clients:1'),
  exists: jest.fn(),
  on: jest.fn(),
};

jest.mock('redis', () => ({
  createClient: jest.fn(() => mockRedisClient),
}));

describe('Redis Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Redis Client', () => {
    it('should create Redis client with correct configuration', async () => {
      const { createClient } = await import('redis');
      const { connectRedis } = await import('../src/lib/redis');
      
      await connectRedis();
      
      expect(createClient).toHaveBeenCalledWith({
        url: process.env.REDIS_URL || 'redis://localhost:6379'
      });
    });

    it('should handle Redis connection gracefully', async () => {
      const { connectRedis } = await import('../src/lib/redis');
      
      mockRedisClient.isOpen = false;
      await connectRedis();
      
      expect(mockRedisClient.connect).toHaveBeenCalled();
    });

    it('should not reconnect if already connected', async () => {
      const { connectRedis } = await import('../src/lib/redis');
      
      mockRedisClient.isOpen = true;
      await connectRedis();
      
      expect(mockRedisClient.connect).not.toHaveBeenCalled();
    });
  });

  describe('Redis Utils', () => {
    it('should set cache with expiration', async () => {
      const { setCache } = await import('../src/lib/redis-utils');
      
      await setCache('test-key', 'test-value', 3600);
      
      expect(mockRedisClient.set).toHaveBeenCalledWith('test-key', 'test-value', { EX: 3600 });
    });

    it('should set cache without expiration', async () => {
      const { setCache } = await import('../src/lib/redis-utils');
      
      await setCache('test-key', 'test-value');
      
      expect(mockRedisClient.set).toHaveBeenCalledWith('test-key', 'test-value');
    });

    it('should get cache value', async () => {
      const { getCache } = await import('../src/lib/redis-utils');
      
      mockRedisClient.get.mockResolvedValue('cached-value');
      const result = await getCache('test-key');
      
      expect(mockRedisClient.get).toHaveBeenCalledWith('test-key');
      expect(result).toBe('cached-value');
    });

    it('should delete cache key', async () => {
      const { deleteCache } = await import('../src/lib/redis-utils');
      
      await deleteCache('test-key');
      
      expect(mockRedisClient.del).toHaveBeenCalledWith('test-key');
    });

    it('should get keys by pattern', async () => {
      const { getKeysByPattern } = await import('../src/lib/redis-utils');
      
      mockRedisClient.keys.mockResolvedValue(['key1', 'key2']);
      const result = await getKeysByPattern('test:*');
      
      expect(mockRedisClient.keys).toHaveBeenCalledWith('test:*');
      expect(result).toEqual(['key1', 'key2']);
    });

    it('should delete keys by pattern', async () => {
      const { deleteKeysByPattern } = await import('../src/lib/redis-utils');
      
      mockRedisClient.keys.mockResolvedValue(['key1', 'key2']);
      mockRedisClient.del.mockResolvedValue(2);
      
      const result = await deleteKeysByPattern('test:*');
      
      expect(mockRedisClient.keys).toHaveBeenCalledWith('test:*');
      expect(mockRedisClient.del).toHaveBeenCalledWith(['key1', 'key2']);
      expect(result).toBe(2);
    });

    it('should handle empty pattern deletion', async () => {
      const { deleteKeysByPattern } = await import('../src/lib/redis-utils');
      
      mockRedisClient.keys.mockResolvedValue([]);
      
      const result = await deleteKeysByPattern('test:*');
      
      expect(result).toBe(0);
      expect(mockRedisClient.del).not.toHaveBeenCalled();
    });

    it('should check cache validity', async () => {
      const { isCacheValid } = await import('../src/lib/redis-utils');
      
      mockRedisClient.exists.mockResolvedValue(1);
      const result = await isCacheValid('test-key');
      
      expect(mockRedisClient.exists).toHaveBeenCalledWith('test-key');
      expect(result).toBe(true);
    });

    it('should set cache with metadata', async () => {
      const { setCacheWithMetadata } = await import('../src/lib/redis-utils');
      
      const data = { test: 'data' };
      const metadata = { source: 'test' };
      
      await setCacheWithMetadata('test-key', data, metadata, 3600);
      
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'test-key',
        expect.stringContaining('"data":{"test":"data"}'),
        { EX: 3600 }
      );
    });

    it('should get cache with metadata', async () => {
      const { getCacheWithMetadata } = await import('../src/lib/redis-utils');
      
      const cacheData = {
        data: { test: 'data' },
        metadata: { timestamp: Date.now() }
      };
      
      mockRedisClient.get.mockResolvedValue(JSON.stringify(cacheData));
      const result = await getCacheWithMetadata('test-key');
      
      expect(result).toEqual(cacheData);
    });
  });

  describe('Cache Manager', () => {
    it('should get cache statistics', async () => {
      const { CacheManager } = await import('../src/lib/cache-manager');
      
      const stats = await CacheManager.getStats();
      
      expect(mockRedisClient.info).toHaveBeenCalled();
      expect(stats).toHaveProperty('totalKeys');
      expect(stats).toHaveProperty('memoryUsage');
      expect(stats).toHaveProperty('lastSave');
      expect(stats).toHaveProperty('connectedClients');
    });

    it('should invalidate resource cache', async () => {
      const { CacheManager } = await import('../src/lib/cache-manager');
      
      mockRedisClient.keys.mockResolvedValue(['cache:test1', 'cache:test2']);
      mockRedisClient.del.mockResolvedValue(2);
      
      const result = await CacheManager.invalidateResource('test');
      
      expect(mockRedisClient.keys).toHaveBeenCalledWith('cache:*test*');
      expect(result).toBe(2);
    });

    it('should invalidate all caches', async () => {
      const { CacheManager } = await import('../src/lib/cache-manager');
      
      await CacheManager.invalidateAll();
      
      expect(mockRedisClient.flushAll).toHaveBeenCalled();
    });

    it('should get all cache keys', async () => {
      const { CacheManager } = await import('../src/lib/cache-manager');
      
      mockRedisClient.keys.mockResolvedValue(['cache:key1', 'cache:key2']);
      const result = await CacheManager.getAllKeys();
      
      expect(mockRedisClient.keys).toHaveBeenCalledWith('cache:*');
      expect(result).toEqual(['cache:key1', 'cache:key2']);
    });
  });
});
