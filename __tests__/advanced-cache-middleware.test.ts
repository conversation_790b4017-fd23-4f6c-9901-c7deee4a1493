import { Request, Response, NextFunction } from 'express';
import { jest } from '@jest/globals';

// Mock Redis utils
const mockGetCache = jest.fn();
const mockSetCache = jest.fn();

jest.mock('../src/lib/redis-utils', () => ({
  getCache: mockGetCache,
  setCache: mockSetCache,
}));

describe('Advanced Cache Middleware', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;
  let jsonSpy: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    
    jsonSpy = jest.fn();
    
    mockReq = {
      method: 'GET',
      originalUrl: '/api/v1/test',
      query: {},
      body: {},
      headers: {
        authorization: 'Bearer test-token-123'
      }
    };
    
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jsonSpy,
    };
    
    mockNext = jest.fn();
  });

  describe('advancedCacheMiddleware', () => {
    it('should skip caching for non-GET requests', async () => {
      const { advancedCacheMiddleware } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockReq.method = 'POST';
      const middleware = advancedCacheMiddleware(3600);
      
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockGetCache).not.toHaveBeenCalled();
    });

    it('should return cached response if available', async () => {
      const { advancedCacheMiddleware } = await import('../src/middleware/advancedCacheMiddleware');
      
      const cachedData = {
        data: { test: 'cached-data' },
        hash: 'test-hash',
        timestamp: Date.now()
      };
      
      mockGetCache.mockResolvedValue(JSON.stringify(cachedData));
      
      const middleware = advancedCacheMiddleware(3600);
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockGetCache).toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith(cachedData.data);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should proceed to next middleware if no cache exists', async () => {
      const { advancedCacheMiddleware } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockGetCache.mockResolvedValue(null);
      
      const middleware = advancedCacheMiddleware(3600);
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockGetCache).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should cache response data when json is called', async () => {
      const { advancedCacheMiddleware } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockGetCache.mockResolvedValue(null);
      
      const originalJson = mockRes.json;
      const middleware = advancedCacheMiddleware(3600);
      
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      // Simulate calling the modified json method
      const testData = { test: 'response-data' };
      if (mockRes.json) {
        (mockRes.json as any)(testData);
      }
      
      expect(mockSetCache).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle cache errors gracefully', async () => {
      const { advancedCacheMiddleware } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockGetCache.mockRejectedValue(new Error('Redis connection failed'));
      
      const middleware = advancedCacheMiddleware(3600);
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
    });

    it('should generate consistent cache keys for same requests', async () => {
      const { advancedCacheMiddleware } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockGetCache.mockResolvedValue(null);
      
      const middleware = advancedCacheMiddleware(3600);
      
      // Call middleware twice with same request
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      // Should call getCache with same key both times
      expect(mockGetCache).toHaveBeenCalledTimes(2);
      const firstCall = mockGetCache.mock.calls[0][0];
      const secondCall = mockGetCache.mock.calls[1][0];
      expect(firstCall).toBe(secondCall);
    });

    it('should generate different cache keys for different requests', async () => {
      const { advancedCacheMiddleware } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockGetCache.mockResolvedValue(null);
      
      const middleware = advancedCacheMiddleware(3600);
      
      // First request
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      // Second request with different URL
      const mockReq2 = { ...mockReq, originalUrl: '/api/v1/different' };
      await middleware(mockReq2 as Request, mockRes as Response, mockNext);
      
      expect(mockGetCache).toHaveBeenCalledTimes(2);
      const firstCall = mockGetCache.mock.calls[0][0];
      const secondCall = mockGetCache.mock.calls[1][0];
      expect(firstCall).not.toBe(secondCall);
    });
  });

  describe('updateCacheOnChange', () => {
    it('should skip for GET requests', async () => {
      const { updateCacheOnChange } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockReq.method = 'GET';
      const middleware = updateCacheOnChange(3600);
      
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockSetCache).not.toHaveBeenCalled();
    });

    it('should handle POST requests', async () => {
      const { updateCacheOnChange } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockReq.method = 'POST';
      mockReq.baseUrl = '/api/v1/zones';
      
      const middleware = updateCacheOnChange(3600);
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle PUT requests', async () => {
      const { updateCacheOnChange } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockReq.method = 'PUT';
      mockReq.baseUrl = '/api/v1/zones';
      
      const middleware = updateCacheOnChange(3600);
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
    });

    it('should handle DELETE requests', async () => {
      const { updateCacheOnChange } = await import('../src/middleware/advancedCacheMiddleware');
      
      mockReq.method = 'DELETE';
      mockReq.baseUrl = '/api/v1/zones';
      
      const middleware = updateCacheOnChange(3600);
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
    });
  });
});
