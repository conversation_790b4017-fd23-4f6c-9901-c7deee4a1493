openapi: 3.0.3
info:
    title: Emissium API
    description: >
        Welcome to the Emissium API documentation. This page contains all available endpoints for accessing Emissium's services. <br /> <br />
        Please ensure all requests are authorized with a valid user token (`BearerAuth`). If you do not have a user token, consider subscribing to an
        <a href="https://emissium.io/"> Emissium data plan </a> or contact our team for assistance at
        <strong> <a href="mailto:<EMAIL>"><EMAIL></a> </strong>
    version: 1.0.0
servers:
    - url: https://data.emissium.io
    - url: http://localhost:2999
components:
    securitySchemes:
        BearerAuth:
            type: http
            scheme: bearer
            bearerFormat: JWT
    schemas:
        full_nuts_info:
            type: object
            properties:
                nuts_id:
                    type: string
                name:
                    type: string
                is_active:
                    type: boolean
                nuts_level:
                    type: string
                children:
                    type: array
                    items:
                        oneOf:
                            - type: string
                            - $ref: '#/components/schemas/nuts_info'
            example:
                nuts_id: 'IT'
                name: 'Italy'
                is_active: true
                nuts_level: '0'
                children:
                    - 'ITG'
                    - 'ITH'
                    - 'ITI'
                    - 'ITC'
                    - 'ITF'

        nuts_info:
            type: object
            properties:
                nuts_id:
                    type: string
                name:
                    type: string
                is_active:
                    type: boolean
            example:
                nuts_id: 'ITG'
                name: 'Isole'
                is_active: true

        impact_info:
            type: array
            items:
                type: object
                properties:
                    id:
                        type: string
                    indicator:
                        type: string
                    metadata:
                        type: object
                        properties:
                            methodology:
                                type: string
                            category:
                                type: string
                            units:
                                type: string
                            system_model:
                                type: string
                            model_type:
                                type: string
                            alternative_name:
                                type: string
                    is_available:
                        type: boolean
            example:
                - id: 'gwp100a'
                  indicator: 'Global Warming Potential over 100 years - Total'
                  metadata:
                      methodology: 'IPCC 2021'
                      units: 'gCO2-eq/kWh'
                      category: 'Climate Change: Total (including Short-Lived Climate Forcers)'
                      system_model: 'Allocation cut-off by classification'
                      model_type: 'Attributional'
                      alternative_name: 'Carbon intensity 100a'
                  is_available: true
                - id: 'eco_footprint'
                  indicator: 'Ecological Footprint - Global Hectares'
                  metadata:
                      methodology: 'Ecological Footprint'
                      units: 'm2a/kWh'
                      category: 'Total'
                      system_model: 'Allocation cut-off by classification'
                      model_type: 'Attributional'
                      alternative_name: 'Ecological Footprint'
                  is_available: true

        impact_sample:
            type: object
            properties:
                dt:
                    type: string
                    format: date-time
                consumption:
                    type: number
                production:
                    type: number

        power_breakdown_sample:
            type: object
            properties:
                dt:
                    type: string
                    format: date-time
                power_breakdown:
                    type: object
                    properties:
                        consumption:
                            type: object
                            properties:
                                solar:
                                    type: number
                                wind:
                                    type: number
                                hydro:
                                    type: number
                                nuclear:
                                    type: number
                                gas:
                                    type: number
                                coal:
                                    type: number
                                oil:
                                    type: number
                                biomass:
                                    type: number
                        production:
                            type: object
                            properties:
                                solar:
                                    type: number
                                wind:
                                    type: number
                                hydro:
                                    type: number
                                nuclear:
                                    type: number
                                gas:
                                    type: number
                                coal:
                                    type: number
                                oil:
                                    type: number
                                biomass:
                                    type: number

        impact_time_series:
            type: object
            properties:
                id:
                    type: string
                indicator:
                    type: string
                units:
                    type: string
                mode:
                    type: string
                nuts_id:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/impact_sample'
            example:
                id: 'gwp100a'
                indicator: 'Global Warming Potential over 100 years - Total'
                units: 'gCO2-eq/kWh'
                mode: 'last'
                nuts_id: 'DE'
                data:
                    - dt: '2025-01-01T00:00:00Z'
                      consumption: 64
                      production: 77

        power_breakdown_time_series:
            type: object
            properties:
                id:
                    type: string
                indicator:
                    type: string
                units:
                    type: string
                mode:
                    type: string
                nuts_id:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/power_breakdown_sample'
            example:
                id: 'power_breakdown'
                indicator: 'Electricity consumption breakdown'
                units: '-'
                mode: 'last'
                nuts_id: 'DE'
                data:
                    - dt: '2025-01-01T00:00:00Z'
                      power_breakdown:
                          consumption:
                              solar: 0.50000
                              wind: 0.30000
                              hydro: 0.10000
                              nuclear: 0.05000
                              gas: 0.02000
                              coal: 0.01000
                              oil: 0.01000
                              biomass: 0.01000
                          production:
                              solar: 0.50000
                              wind: 0.30000
                              hydro: 0.10000
                              nuclear: 0.05000
                              gas: 0.02000
                              coal: 0.01000
                              oil: 0.01000
                              biomass: 0.01000

        total_power_data:
            type: object
            properties:
                id:
                    type: string
                    example: 'power'
                indicator:
                    type: string
                    example: 'Power Consumption'
                units:
                    type: string
                    example: 'kWh'
                mode:
                    type: string
                    enum: [last24h, historical]
                nuts_id:
                    oneOf:
                        - type: string
                          example: UKI1
                        - type: array
                          items:
                              type: string
                          example: [UKI1, UKI2]
                data:
                    type: object
                    additionalProperties:
                        type: array
                        items:
                            type: object
                            properties:
                                dt:
                                    type: string
                                    format: date-time
                                value:
                                    type: number
                                value_per_capita:
                                    type: number
                type:
                    type: string
                    enum: [consumption, production]

        calculate_emissions_request:
            type: object
            required:
                - consumption_profile
                - frequency
                - nuts_id
                - units
            properties:
                consumption_profile:
                    type: array
                    items:
                        type: object
                        required:
                            - timestamp
                            - value
                        properties:
                            timestamp:
                                type: string
                                format: date-time
                                example: '2024-03-20T10:00:00Z'
                            value:
                                type: number
                                example: 100.5
                frequency:
                    type: string
                    enum: [15m, 30m, 1h, 1d]
                    example: '1h'
                nuts_id:
                    type: string
                    example: 'DE'
                units:
                    type: string
                    enum: [kWh]
                    example: 'kWh'
                mode:
                    type: string
                    example: 'ipcc_2021_climate_change_gwp100a'

        calculate_emissions_response:
            type: object
            properties:
                indicator:
                    type: string
                    example: 'Global Warming Potential (100 years)'
                units:
                    type: string
                    example: 'kgCO2-eq'
                frequency:
                    type: string
                    example: '1h'
                total:
                    type: number
                    example: 45.67
                profile:
                    type: array
                    items:
                        type: object
                        properties:
                            timestamp:
                                type: string
                                format: date-time
                                example: '2024-03-20T10:00:00Z'
                            value:
                                type: number
                                example: 45.67

security:
    - BearerAuth: []
paths:
    /system/heartbeat:
        get:
            summary: Check system availability
            description: >
                Checks the system's operational status. It should be used for health checks
                to verify that the API and its resources are available.
            tags:
                - system
            responses:
                '200':
                    description: |
                        __Success__ <br />
                        System is fully operational.
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: string
                                        example: 'success'
                                    code:
                                        type: integer
                                        example: 200
                                    message:
                                        type: string
                                        example: 'System is responding and database connections are available.'
                '401':
                    description: &unauthorized_message >
                        __Unauthorized__ <br />
                        Authorization is required to access this resource. Ensure a valid user token is included in the request headers.
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: string
                                        example: 'error'
                                    code:
                                        type: integer
                                        example: 401
                                    message:
                                        type: string
                                        example: 'Authorization is required to access this resource. Ensure a valid user token is included in the request headers.'
                '500':
                    description: >
                        __Internal Server Error__ <br />
                        The system is not responding. This could indicate system downtime
                        or issues with database connectivity. For further assistance, please contact support.
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: string
                                        example: 'error'
                                    code:
                                        type: integer
                                        example: 500
                                    message:
                                        type: string
                                        example: 'System is not responding properly. This could indicate system downtime or issues with database connectivity. For further assistance, please contact support.'
                                    error:
                                        type: string
                                        example: 'Error message detailing the issue'

    /zones:
        get:
            summary: Get all NUTS zones
            description: >
                Retrieves a list of NUTS zones based on the specified `status` and `level` filters.
                <br /> Zones data is limited to 5 zones in the response for brevity. The complete list is not displayed here.
            tags:
                - zones
            security:
                - BearerAuth: []
            parameters:
                - name: status
                  in: query
                  required: false
                  description: |
                      __Optional__ Filters zones based on their status. <br />
                      If omitted, returns both active and inactive zones.
                  schema:
                      type: string
                      enum: [active, inactive]
                - name: level
                  in: query
                  description: |
                      __Optional__ Filters zones by NUTS level. <br />
                      If omitted, returns all NUTS levels.
                  schema:
                      type: string
                      enum: [0, 1, 2, 3]
            responses:
                '200':
                    description: |
                        __Success__ <br />
                        Returns a list of NUTS zones matching the specified filters.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/nuts_info'
                '401':
                    description: *unauthorized_message
                '404':
                    description: >
                        __Not Found__ <br />
                        No zones were found matching the specified parameters.
                '500':
                    description: &internal_server_error_message >
                        __Internal Server Error__ <br />
                        An internal server error occurred. Please try again later or contact support if the issue persists.

    /zones/{nuts_id}:
        get:
            summary: Get zone details
            description: >
                Retrieves details of a specified NUTS zone by its unique identifier.
                Optionally, includes detailed information about its child zones if requested.
            tags:
                - zones
            security:
                - BearerAuth: []
            parameters:
                - name: nuts_id
                  in: path
                  required: true
                  description: >
                      The NUTS ID of the zone to retrieve. Case-sensitive. For example, `DE` for Germany, `IT` for Italy.
                  schema:
                      type: string
                - name: detailed_children
                  in: query
                  description: >
                      _Optional._ Specifies whether to include details about the child zones.
                      Defaults to false if not specified.
                  schema:
                      type: boolean
            responses:
                '200':
                    description: |
                        __Success__ <br />
                        Successfully retrieved the NUTS zone details. The response includes information
                        such as the zone's name, activity status, level, and optionally, a list of child zones.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/full_nuts_info'
                '400':
                    description: >
                        __Bad Request__ <br />
                        The `nuts_id` parameter is missing or invalid. Ensure that a valid NUTS ID is provided in the request.
                '401':
                    description: *unauthorized_message
                '404':
                    description: >
                        __Not Found__ <br />
                        No data was found for the specified `nuts_id`. Verify the ID and try again.
                '500':
                    description: *internal_server_error_message

    /impacts:
        get:
            summary: Retrieve supported environmental indicators
            description: >
                Retrieves a list of available environmental indicators, including their methodology, measurement units,
                and availability status. This endpoint can be used to understand the various metrics supported by
                Emissium for environmental analysis.
            tags:
                - indicators
            security:
                - BearerAuth: []
            responses:
                '200':
                    description: |
                        __Success__ <br />
                        Successfully retrieved the list of indicators. Each indicator object includes the
                        indicator ID, methodology, units of measurement, and availability status.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/impact_info'
                '401':
                    description: *unauthorized_message
                '500':
                    description: *internal_server_error_message

    /impacts/{indicator}/{mode}/{nuts_id}:
        get:
            summary: Retrieve specific data for an indicator, mode and nuts_id.
            description: >
                This endpoint retrieves data for a specified environmental indicator, mode, and NUTS zone.
                Depending on the selected mode, you can access the latest available data (last), the most recent 24-hour data (last24h), or historical data (historical).
                <br /> Impacts data is limited to 5 data points in the response for brevity. The complete list is not displayed here.
            tags:
                - indicators
            security:
                - BearerAuth: []
            parameters:
                - name: indicator
                  in: path
                  required: true
                  description: The environmental indicator for which data is requested.
                  schema:
                      type: string
                      enum:
                          [
                              gwp100a,
                              gwp100a_scope2,
                              gwp100a_scope3,
                              gwp20a,
                              gwp500a,
                              ne_share_direct,
                              ne_share,
                              re_share_direct,
                              re_share,
                              eco_footprint,
                              eco_scarcity
                          ]
                - name: mode
                  in: path
                  required: true
                  description: Specifies the timeframe for the data.
                  schema:
                      type: string
                      enum: [last, last24h, historical]
                - name: nuts_id
                  in: path
                  required: true
                  description: >
                      The NUTS zone identifier(s). Can be a single code (e.g., `DE123`) or
                      multiple codes separated by commas (`DE123,ES456`)
                  schema:
                      type: string
                - name: start_date
                  in: query
                  description: Start date for historical data (only valid for `historical` mode). Example - `2025-01-01T00:00:00Z`
                  schema:
                      type: string
                      format: date-time
                      #   example: '2025-01-01T00:00:00Z'
                - name: end_date
                  in: query
                  description: >
                      End date for historical data. Must be ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).
                      Defaults to **35 days** after `start_date` if not specified.
                  schema:
                      type: string
                      format: date-time
                      #   example: '2025-01-02T00:00:00Z'
                - name: include_production
                  in: query
                  description: Include production-related indicators in the response. Defaults to `false`.
                  schema:
                      type: boolean
            responses:
                '200':
                    description: |
                        __Success__ <br />
                        Successfully retrieved data for the specified indicator and zone. The response includes
                        indicator details and time-series data for consumption and (optionally) production.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/impact_time_series'
                '400':
                    description: >
                        __Bad Request__ <br />
                        The request is invalid due to missing or incorrect parameters.
                '401':
                    description: *unauthorized_message
                '404':
                    description: >
                        __Not Found__ <br />
                        The specified `nuts_id` or indicator data could not be found.
                '500':
                    description: *internal_server_error_message

    /power/{mode}/{nuts_id}:
        get:
            summary: Get power consumption or production data
            description: Retrieves power consumption or production data for specified NUTS regions
            tags:
                - indicators
            security:
                - BearerAuth: []
            parameters:
                - name: mode
                  in: path
                  required: true
                  schema:
                    type: string
                    enum: [last, last24h, historical]
                  description: The time range mode (last, last24h, or historical)
                - name: nuts_id
                  in: path
                  required: true
                  description: >
                      The NUTS zone identifier(s). Can be a single code (e.g., `DE123`) or
                      multiple codes separated by commas (`DE123,ES456`)
                  schema:
                      type: string
                - name: start_date
                  in: query
                  description: Start date for historical data (only valid for `historical` mode). Example - `2025-01-01T00:00:00Z`
                  schema:
                      type: string
                      format: date-time
                      # example: '2025-01-01T00:00:00Z'
                - name: end_date
                  in: query
                  description: >
                      End date for historical data. Must be ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).
                      Defaults to **35 days** after `start_date` if not specified.
                  schema:
                      type: string
                      format: date-time
                      # example: '2025-01-01T00:00:00Z'
                - name: type
                  in: query
                  required: false
                  schema:
                    type: string
                    enum: [consumption, production]
                    default: consumption
                  description: Data type (consumption or production)
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    id:
                                        type: string
                                        example: power
                                    indicator:
                                        type: string
                                        example: Power Consumption
                                    units:
                                        type: string
                                        example: kWh
                                    mode:
                                        type: string
                                        example: last24h
                                    nuts_id:
                                        type: string
                                        example: DE
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                dt:
                                                    type: string
                                                    format: date-time
                                                value:
                                                    type: number
                                                value_per_capita:
                                                    type: number
                '400':
                    description: Invalid parameters
                '401':
                    description: Unauthorized
                '403':
                    description: Subscription limitations
                '404':
                    description: No data available
                '500':
                    description: Server error

    /price/{mode}/{nuts_id}:
        get:
            summary: Get price data
            description: Retrieves price data for specified parameters
            tags:
                - indicators
            security:
                - BearerAuth: []
            parameters:
                - name: mode
                  in: path
                  required: true
                  schema:
                    type: string
                    enum: [last, last24h, historical]
                  description: The time range mode (last, last24h, or historical)
                - name: nuts_id
                  in: path
                  required: true
                  description: >
                      The NUTS zone identifier(s). Can be a single code (e.g., `DE123`) or
                      multiple codes separated by commas (`DE123,ES456`)
                  schema:
                      type: string
                - name: start_date
                  in: query
                  description: Start date for historical data (only valid for `historical` mode). Example - `2025-01-01T00:00:00Z`
                  schema:
                      type: string
                      format: date-time
                      # example: '2025-01-01T00:00:00Z'
                - name: end_date
                  in: query
                  description: >
                      End date for historical data. Must be ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).
                      Defaults to **35 days** after `start_date` if not specified.
                  schema:
                      type: string
                      format: date-time
                      # example: '2025-01-01T00:00:00Z'
            responses:
                '200':
                    description: Successful response
                '400':
                    description: Invalid parameters
                '401':
                    description: Unauthorized
                '500':
                    description: Server error

    /calculate:
        post:
            summary: Calculate environmental impact
            description: Calculate environmental impact based on provided parameters
            tags:
                - indicators
            security:
                - BearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - nuts_id
                                - indicator
                                - value
                            properties:
                                nuts_id:
                                    type: string
                                indicator:
                                    type: string
                                value:
                                    type: number
                        example:
                            consumption_profile: [
                                {"timestamp": "2025-01-01T00:00:00Z", "value": 10},
                                {"timestamp": "2025-01-01T00:15:00Z", "value": 15},
                                {"timestamp": "2025-01-01T00:30:00Z", "value": 12},
                                {"timestamp": "2025-01-01T00:45:00Z", "value": 8},
                                {"timestamp": "2025-01-01T01:00:00Z", "value": 5},
                                {"timestamp": "2025-01-01T01:15:00Z", "value": 7},
                                {"timestamp": "2025-01-01T01:30:00Z", "value": 9},
                                {"timestamp": "2025-01-01T01:45:00Z", "value": 11}
                            ]
                            frequency: "15min"
                            nuts_id: "DE"
                            units: "kWh"
            responses:
                '200':
                    description: Successful calculation
                '400':
                    description: Invalid input
                '401':
                    description: Unauthorized
                '500':
                    description: Server error

    /power_breakdown/{mode}/{nuts_id}:
        get:
            summary: Retrieve power breakdown data for a specific mode and nuts_id
            description: >
                This endpoint retrieves **power breakdown** data for a specified mode and NUTS zone.
                Depending on the selected mode, you can access the latest available data (last), the most recent 24-hour data (last24h), or historical data (historical).
                <br /> Power breakdown data is limited to 5 data points in the response for brevity. The complete list is not displayed here.
            tags:
                - indicators
            security:
                - BearerAuth: []
            parameters:
                - name: mode
                  in: path
                  required: true
                  description: Specifies the timeframe for the data.
                  schema:
                      type: string
                      enum: [last, last24h, historical]
                - name: nuts_id
                  in: path
                  required: true
                  description: >
                      The NUTS zone identifier(s). Can be a single code (e.g., `DE123`) or
                      multiple codes separated by commas (`DE123,ES456`)
                  schema:
                      type: string
                - name: start_date
                  in: query
                  description: Start date for historical data (only valid for `historical` mode). Example - `2025-01-01T00:00:00Z`
                  schema:
                      type: string
                      format: date-time
                      #   example: '2025-01-01T00:00:00Z'
                - name: end_date
                  in: query
                  description: >
                      End date for historical data. Must be ISO8601 format (yyyy-mm-ddTHH:MM:SSZ).
                      Defaults to **35 days** after `start_date` if not specified.
                  schema:
                      type: string
                      format: date-time
                      #   example: '2025-01-01T00:00:00Z'
                - name: include_production
                  in: query
                  description: Include a breakdown of electricity production by power source in the response. Defaults to false.
                  schema:
                      type: boolean

            responses:
                '200':
                    description: |
                        __Success__ <br />
                        Successfully retrieved power breakdown data for the specified mode and zone(s).
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/impact_time_series'
                '400':
                    description: >
                        __Bad Request__ <br />
                        The request is invalid due to missing or incorrect parameters.
                '401':
                    description: *unauthorized_message
                '404':
                    description: >
                        __Not Found__ <br />
                        The specified `nuts_id` or data could not be found.
                '500':
                    description: *internal_server_error_message

    /geometries:
        get:
            summary: Get all nuts 0 zones geometries
            description: >
                Retrieves geometries for all NUTS 0 zones, filtered by their status.
                <br /> Geometry data is limited to 3 zones in the response for brevity. The complete list is not displayed here.
            tags:
                - geometries
            security:
                - BearerAuth: []
            parameters:
                - name: status
                  in: query
                  description: >
                      Specifies whether to filter zones by active or inactive status. If omitted, defaults to `active`.
                  schema:
                      type: string
            responses:
                '200':
                    description: >
                        __Success__ <br />
                        Successfully retrieved the geometries for the specified NUTS zones. The response includes
                        geometrical data for each zone based on the specified status.
                '400':
                    description: >
                        __Bad Request__ <br />
                        The request contains a missing or invalid query parameter (e.g., unsupported `status` value).
                '401':
                    description: *unauthorized_message
                '404':
                    description: >
                        __Not Found__ <br />
                        No geometries found for the specified status.
                '500':
                    description: *internal_server_error_message

    /geometries/{nuts_id}:
        get:
            summary: Get individual zone geometry
            description: >
                Retrieves the geometry for an individual NUTS zone specified by the `nuts_id`.
            tags:
                - geometries
            security:
                - BearerAuth: []
            parameters:
                - name: nuts_id
                  in: path
                  required: true
                  description: >
                      The unique identifier for the NUTS zone. The value is case-sensitive and must match
                      the exact NUTS code (e.g., `DE` for Germany).
                  schema:
                      type: string
            responses:
                '200':
                    description: >
                        __Success__ <br />
                        Successfully retrieved the geometry for the specified NUTS zone. The response includes
                        geographical data, such as coordinates and boundaries, for the zone.
                '400':
                    description: >
                        __Bad Request__ <br />
                        The request contains a missing or invalid `nuts_id` path parameter. Verify that the `nuts_id` is correct and case-sensitive.
                '401':
                    description: *unauthorized_message
                '404':
                    description: >
                        __Not Found__ <br />
                        The specified nuts_id does not match any existing zone. Verify the ID and try again.
                '500':
                    description: *internal_server_error_message
